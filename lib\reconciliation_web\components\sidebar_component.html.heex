<aside class="w-64 bg-white border-r border-gray-200 flex flex-col h-full shadow-sm">
  <!-- Logo Section -->
  <div class="px-6 py-8 border-b border-gray-100">
    <div class="flex items-center group cursor-pointer">
      <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg transition-transform duration-200 group-hover:scale-105">
        <span class="text-white font-bold text-lg">R</span>
      </div>
      <div class="ml-3">
        <h1 class="text-xl font-bold bg-gradient-to-r from-emerald-600 to-slate-600 bg-clip-text text-transparent transition-all duration-200 group-hover:from-emerald-500 group-hover:to-slate-500">
          ReconcilePro
        </h1>
        <p class="text-xs text-gray-500 font-medium transition-colors duration-200 group-hover:text-gray-600">Financial Platform</p>
      </div>
    </div>
  </div>

  <!-- User Profile Section -->
  <div class="px-6 py-4 border-b border-gray-100">
    <div class="flex items-center">
      <div class="w-8 h-8 bg-gradient-to-br from-slate-400 to-slate-500 rounded-full flex items-center justify-center">
        <span class="text-white font-semibold text-sm">
          <%= if @current_user.email do %>
            <%= String.first(@current_user.email) |> String.upcase() %>
          <% else %>
            U
          <% end %>
        </span>
      </div>
      <div class="ml-3 flex-1 min-w-0">
        <p class="text-sm font-semibold text-gray-900 truncate">
          <%= if @current_user.email do %>
            <%= String.split(@current_user.email, "@") |> List.first() %>
          <% else %>
            User
          <% end %>
        </p>
        <p class="text-xs text-gray-500 truncate">
          <%= if @current_user.email do %>
            <%= @current_user.email %>
          <% else %>
            <EMAIL>
          <% end %>
        </p>
      </div>
    </div>
  </div>

  <!-- Quick Status -->
  <div class="px-6 py-3 bg-gradient-to-r from-emerald-50 to-slate-50 border-b border-gray-100">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <div class="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
        <span class="ml-2 text-xs font-medium text-gray-600">System Online</span>
      </div>
      <div class="text-xs text-gray-500">
        <%= Date.utc_today() |> Calendar.strftime("%b %d") %>
      </div>
    </div>
  </div>

  <!-- Navigation -->
  <nav class="flex-1 px-4 py-6 space-y-2">
    <div class="space-y-1">
      <!-- Dashboard -->
      <a href={~p"/dashboard"} class={nav_class(@current_path || "/", "/dashboard")}>
        <.icon name="hero-home" class={icon_class(@current_path || "/", "/dashboard")} />
        <span>Dashboard</span>
        <div class={active_indicator_class(@current_path || "/", "/dashboard")}></div>
      </a>

      <!-- Summary -->
      <a href={~p"/summary"} class={nav_class(@current_path || "/", "/summary")}>
        <.icon name="hero-chart-pie" class={icon_class(@current_path || "/", "/summary")} />
        <span>Summary</span>
        <div class={active_indicator_class(@current_path || "/", "/summary")}></div>
      </a>

      <!-- Reconciliation -->
      <a href={~p"/reconciliation"} class={nav_class(@current_path || "/", "/reconciliation")}>
        <.icon name="hero-document-check" class={icon_class(@current_path || "/", "/reconciliation")} />
        <span>Reconciliation</span>
        <div class={active_indicator_class(@current_path || "/", "/reconciliation")}></div>
      </a>

      <!-- Transactions -->
      <a href={~p"/transactions"} class={nav_class(@current_path || "/", "/transactions")}>
        <.icon name="hero-banknotes" class={icon_class(@current_path || "/", "/transactions")} />
        <span>Transactions</span>
        <div class={active_indicator_class(@current_path || "/", "/transactions")}></div>
      </a>

      <!-- Reports -->
      <a href={~p"/reports"} class={nav_class(@current_path || "/", "/reports")}>
        <.icon name="hero-chart-bar" class={icon_class(@current_path || "/", "/reports")} />
        <span>Reports</span>
        <div class={active_indicator_class(@current_path || "/", "/reports")}></div>
      </a>
    </div>

    <!-- Divider -->
    <div class="border-t border-gray-200 pt-4 mt-6">
      <div class="space-y-1">
        <!-- Settings -->
        <a href={~p"/settings"} class={nav_class(@current_path || "/", "/settings", "slate")}>
          <.icon name="hero-cog-6-tooth" class={icon_class(@current_path || "/", "/settings", "slate")} />
          <span>Settings</span>
          <div class={active_indicator_class(@current_path || "/", "/settings", "slate")}></div>
        </a>
      </div>
    </div>
  </nav>

  <!-- Logout Section -->
  <div class="px-4 py-4 border-t border-gray-100">
    <.form action={~p"/users/log_out"} method="delete" class="w-full">
      <button type="submit"
              class="w-full group flex items-center px-3 py-2.5 text-sm font-medium rounded-xl transition-all duration-200 hover:bg-red-50 hover:text-red-700 text-gray-700 hover:shadow-sm">
        <.icon name="hero-arrow-left-on-rectangle" class="w-5 h-5 mr-3 text-gray-400 group-hover:text-red-600" />
        <span>Sign Out</span>
        <div class="ml-auto w-2 h-2 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></div>
      </button>
    </.form>

    <!-- Version Info -->
    <div class="mt-4 px-3 py-2 text-center">
      <p class="text-xs text-gray-400 font-medium">v1.0.0</p>
      <p class="text-xs text-gray-300">© 2025 ReconcilePro</p>
    </div>
  </div>
</aside>