<%= if @current_user do %>
  <div class="flex h-screen">
    <.live_component module={ReconciliationWeb.SidebarComponent} id="sidebar" current_user={@current_user} current_path={assigns[:current_path] || "/"} />

    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <.live_component module={ReconciliationWeb.NavbarComponent} id="navbar" current_user={@current_user} />

      <!-- Page Content -->
      <main class="flex-1 overflow-x-hidden overflow-y-auto py-4">
        <div class="max-w-6xl mx-auto">
          <.flash_group flash={@flash} />
          <%= @inner_content %>
        </div>
      </main>
    </div>
  </div>
<% else %>
  <!-- Layout for logged-out users (original app.html.heex content) -->
  <main class="px-4 py-20 sm:px-6 lg:px-8">
    <div class="mx-auto max-w-2xl">
      <.flash_group flash={@flash} />
      <%= @inner_content %>
    </div>
  </main>
<% end %>
