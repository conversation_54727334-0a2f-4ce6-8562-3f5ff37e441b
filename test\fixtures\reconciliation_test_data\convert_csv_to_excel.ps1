# PowerShell script to convert CSV files to Excel format
# Requires Microsoft Excel to be installed

param(
    [string]$CsvDirectory = ".\csv",
    [string]$ExcelDirectory = ".\excel"
)

# Ensure Excel directory exists
if (!(Test-Path $ExcelDirectory)) {
    New-Item -ItemType Directory -Path $ExcelDirectory -Force
}

try {
    # Create Excel application object
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    Write-Host "Excel application created successfully"
    
    # Get all CSV files
    $csvFiles = Get-ChildItem -Path $CsvDirectory -Filter "*.csv"
    
    if ($csvFiles.Count -eq 0) {
        Write-Host "No CSV files found in $CsvDirectory"
        exit 1
    }
    
    Write-Host "Found $($csvFiles.Count) CSV files to convert"
    
    $successCount = 0
    
    foreach ($csvFile in $csvFiles) {
        try {
            $csvPath = $csvFile.FullName
            $excelFileName = $csvFile.BaseName + ".xlsx"
            $excelPath = Join-Path $ExcelDirectory $excelFileName
            
            Write-Host "Converting: $($csvFile.Name) -> $excelFileName"
            
            # Open CSV file
            $workbook = $excel.Workbooks.Open($csvPath)
            
            # Save as Excel format
            $workbook.SaveAs($excelPath, 51) # 51 = xlOpenXMLWorkbook (.xlsx)
            $workbook.Close()
            
            Write-Host "Successfully converted: $excelFileName"
            $successCount++
        }
        catch {
            Write-Host "Error converting $($csvFile.Name): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "`nConversion complete: $successCount/$($csvFiles.Count) files converted successfully"
}
catch {
    Write-Host "Error: Could not create Excel application. Please ensure Microsoft Excel is installed." -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
finally {
    # Clean up Excel application
    if ($excel) {
        $excel.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    }
}
