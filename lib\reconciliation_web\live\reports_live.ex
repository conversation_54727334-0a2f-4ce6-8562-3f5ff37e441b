defmodule ReconciliationWeb.ReportsLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.{ReconciliationRun, Transaction, TransactionMatch}

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_user
    
    # Get reconciliation runs and calculate summary statistics
    reconciliation_runs = Reconciliation.list_reconciliation_runs(user.id)
    summary_stats = calculate_summary_stats(reconciliation_runs)
    
    {:ok,
     socket
     |> assign(:page_title, "Reports")
     |> assign(:reconciliation_runs, reconciliation_runs)
     |> assign(:summary_stats, summary_stats)
     |> assign(:selected_period, "all")
    }
  end

  @impl true
  def handle_event("filter_period", %{"period" => period}, socket) do
    user = socket.assigns.current_user
    
    filtered_runs = case period do
      "last_30" -> filter_runs_by_days(socket.assigns.reconciliation_runs, 30)
      "last_90" -> filter_runs_by_days(socket.assigns.reconciliation_runs, 90)
      "last_year" -> filter_runs_by_days(socket.assigns.reconciliation_runs, 365)
      _ -> socket.assigns.reconciliation_runs
    end
    
    summary_stats = calculate_summary_stats(filtered_runs)
    
    {:noreply,
     socket
     |> assign(:reconciliation_runs, filtered_runs)
     |> assign(:summary_stats, summary_stats)
     |> assign(:selected_period, period)
    }
  end

  def handle_event("export_report", %{"run_id" => run_id}, socket) do
    # TODO: Implement CSV export functionality
    {:noreply, put_flash(socket, :info, "Export functionality coming soon!")}
  end

  # Helper functions
  defp calculate_summary_stats(runs) do
    completed_runs = Enum.filter(runs, &(&1.status == "completed"))
    
    total_runs = length(runs)
    completed_count = length(completed_runs)
    
    # For reconciliation, total transactions should be the sum of the maximum transactions
    # from each run (since matches pair one transaction from A with one from B)
    total_transactions = Enum.sum(Enum.map(completed_runs, &(max(&1.total_transactions_a, &1.total_transactions_b))))
    total_matched = Enum.sum(Enum.map(completed_runs, &(&1.matched_count)))
    
    avg_match_rate = if completed_count > 0 do
      completed_runs
      |> Enum.map(&Decimal.to_float(&1.match_rate))
      |> Enum.sum()
      |> Kernel./(completed_count)
      |> Float.round(1)
    else
      0.0
    end
    
    total_amount_processed = completed_runs
    |> Enum.map(&Decimal.add(&1.total_amount_a, &1.total_amount_b))
    |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
    
    %{
      total_runs: total_runs,
      completed_runs: completed_count,
      total_transactions: total_transactions,
      total_matched: total_matched,
      avg_match_rate: avg_match_rate,
      total_amount_processed: total_amount_processed
    }
  end

  defp filter_runs_by_days(runs, days) do
    cutoff_date = DateTime.utc_now() |> DateTime.add(-days, :day)
    Enum.filter(runs, &(DateTime.compare(&1.inserted_at, cutoff_date) == :gt))
  end

  defp format_currency(amount) when is_nil(amount), do: "0.00"
  defp format_currency(amount) do
    Decimal.to_string(amount, :normal)
  end

  defp format_currency_with_symbol(amount, currency) when is_nil(amount), do: "#{currency_symbol(currency)}0.00"
  defp format_currency_with_symbol(amount, currency) do
    "#{currency_symbol(currency)}#{Decimal.to_string(amount, :normal)}"
  end

  defp currency_symbol(currency) when is_binary(currency) do
    case String.upcase(currency) do
      "USD" -> "$"
      "EUR" -> "€"
      "GBP" -> "£"
      "JPY" -> "¥"
      "CAD" -> "C$"
      "AUD" -> "A$"
      "MWK" -> "MK "
      "ZAR" -> "R "
      "KES" -> "KSh "
      "TZS" -> "TSh "
      "UGX" -> "USh "
      "ZMW" -> "ZK "
      "BWP" -> "P "
      "NAD" -> "N$ "
      "SZL" -> "L "
      "LSL" -> "M "
      other -> "#{other} "
    end
  end
  defp currency_symbol(_), do: ""

  defp format_percentage(percentage) when is_nil(percentage), do: "0%"
  defp format_percentage(percentage) when is_float(percentage), do: "#{percentage}%"
  defp format_percentage(percentage) do
    "#{Decimal.to_string(percentage, :normal)}%"
  end

  defp status_color("completed"), do: "green"
  defp status_color("processing"), do: "yellow"
  defp status_color("failed"), do: "red"
  defp status_color(_), do: "gray"

  defp status_icon("completed"), do: "hero-check-circle"
  defp status_icon("processing"), do: "hero-clock"
  defp status_icon("failed"), do: "hero-x-circle"
  defp status_icon(_), do: "hero-question-mark-circle"

  defp match_rate_color(rate) when rate >= 90, do: "text-green-600"
  defp match_rate_color(rate) when rate >= 70, do: "text-yellow-600"
  defp match_rate_color(_), do: "text-red-600"
end
