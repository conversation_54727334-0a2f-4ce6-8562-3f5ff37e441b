# # defmodule Reconciliation.Services.MatchingEngine do
# #   @moduledoc """
# #   Service for matching transactions between two files in a reconciliation run.
# #   """

# #   require Logger

# #   alias Reconciliation.{
# #     Repo,
# #     Transaction,
# #     TransactionMatch,
# #     ReconciliationRun
# #   }

# #   require Logger

# #   @doc """
# #   Performs transaction matching for a reconciliation run.
# #   """
# #   def match_transactions(%ReconciliationRun{} = run) do
# #     Logger.info("Starting transaction matching for run #{run.id}")

# #     # Get user settings
# #     {:ok, settings} = Reconciliation.get_or_create_settings(run.user_id)

# #     # Get transactions from both files
# #     transactions = Reconciliation.get_transactions(run.id)

# #     file_a_transactions = Enum.filter(transactions, fn t ->
# #       t.uploaded_file.file_type == "file_a"
# #     end)

# #     file_b_transactions = Enum.filter(transactions, fn t ->
# #       t.uploaded_file.file_type == "file_b"
# #     end)

# #     Logger.info("Found #{length(file_a_transactions)} transactions in File A, #{length(file_b_transactions)} in File B")

# #     # Perform matching
# #     matches = find_matches(file_a_transactions, file_b_transactions, settings)

# #     # Create match records and update transaction statuses
# #     create_matches_and_update_transactions(matches, run.id)

# #     # Update reconciliation run statistics
# #     Reconciliation.calculate_reconciliation_stats(run.id)

# #     Logger.info("Completed transaction matching for run #{run.id}")
# #     {:ok, matches}
# #   end

# #   # Find matches between transactions from file A and file B
# #   defp find_matches(file_a_transactions, file_b_transactions, settings) do
# #     Logger.info("Auto-match settings: exact=#{settings.auto_match_exact}, fuzzy=#{settings.auto_match_fuzzy}")

# #     # First pass: Find exact matches if enabled
# #     {exact_matches, remaining_a, remaining_b} = if settings.auto_match_exact do
# #       find_exact_matches(file_a_transactions, file_b_transactions, settings)
# #     else
# #       Logger.info("Auto exact matching is disabled")
# #       {[], file_a_transactions, file_b_transactions}
# #     end

# #     matched_b_ids = exact_matches
# #     |> Enum.map(& &1.transaction_b.id)
# #     |> MapSet.new()

# #     # Second pass: Find fuzzy matches if enabled
# #     fuzzy_matches = if settings.auto_match_fuzzy do
# #       find_fuzzy_matches(remaining_a, remaining_b, matched_b_ids, settings)
# #     else
# #       Logger.info("Auto fuzzy matching is disabled")
# #       []
# #     end

# #     exact_matches ++ fuzzy_matches
# #   end

# #   # Find exact matches based on amount, date, and reference
# #   defp find_exact_matches(file_a_transactions, file_b_transactions, settings) do
# #     Logger.info("Finding exact matches: #{length(file_a_transactions)} A transactions, #{length(file_b_transactions)} B transactions")

# #     matches = for a_txn <- file_a_transactions,
# #                   b_txn <- file_b_transactions,
# #                   is_exact_match?(a_txn, b_txn, settings) do
# #       Logger.debug("Found potential exact match: A(#{a_txn.id}) <-> B(#{b_txn.id})")
# #       %{
# #         transaction_a: a_txn,
# #         transaction_b: b_txn,
# #         match_type: "exact",
# #         confidence_score: Decimal.new("100"),
# #         matching_criteria: build_exact_match_criteria(a_txn, b_txn, settings)
# #       }
# #     end

# #     Logger.info("Found #{length(matches)} potential exact matches before deduplication")

# #     # Remove duplicates and ensure one-to-one matching
# #     unique_matches = ensure_one_to_one_matching(matches)

# #     Logger.info("After deduplication: #{length(unique_matches)} unique exact matches")

# #     matched_a_ids = unique_matches |> Enum.map(& &1.transaction_a.id) |> MapSet.new()
# #     matched_b_ids = unique_matches |> Enum.map(& &1.transaction_b.id) |> MapSet.new()

# #     remaining_a = Enum.reject(file_a_transactions, &MapSet.member?(matched_a_ids, &1.id))
# #     remaining_b = Enum.reject(file_b_transactions, &MapSet.member?(matched_b_ids, &1.id))

# #     Logger.info("Remaining unmatched: #{length(remaining_a)} A transactions, #{length(remaining_b)} B transactions")

# #     {unique_matches, remaining_a, remaining_b}
# #   end

# #   # Find fuzzy matches based on similarity scores
# #   defp find_fuzzy_matches(file_a_transactions, file_b_transactions, already_matched_b_ids, settings) do
# #     threshold = Decimal.to_float(settings.fuzzy_match_threshold)

# #     matches = for a_txn <- file_a_transactions,
# #                   b_txn <- file_b_transactions,
# #                   not MapSet.member?(already_matched_b_ids, b_txn.id) do

# #       similarity = Transaction.similarity_score(a_txn, b_txn, [
# #         amount_tolerance: settings.amount_tolerance,
# #         date_tolerance: settings.date_tolerance_days
# #       ])

# #       if similarity >= threshold * 100 do
# #         criteria = build_matching_criteria(a_txn, b_txn, settings)

# #         %{
# #           transaction_a: a_txn,
# #           transaction_b: b_txn,
# #           match_type: "fuzzy",
# #           confidence_score: Decimal.from_float(similarity),
# #           matching_criteria: criteria
# #         }
# #       else
# #         nil
# #       end
# #     end
# #     |> Enum.reject(&is_nil/1)
# #     |> Enum.sort_by(& Decimal.to_float(&1.confidence_score), :desc)

# #     # Ensure one-to-one matching for fuzzy matches too
# #     ensure_one_to_one_matching(matches)
# #   end

# #   # Check if two transactions are an exact match
# #   # Only require a match for fields that are present in both transactions (not nil in both)
# #   defp is_exact_match?(a_txn, b_txn, settings) do
# #     amount_match = Transaction.amount_match?(a_txn, b_txn, settings.amount_tolerance)
# #     date_match = Transaction.date_match?(a_txn, b_txn, settings.date_tolerance_days)

# #     # Only require reference match if both have a reference
# #     reference_match =
# #       if is_nil(a_txn.reference) and is_nil(b_txn.reference), do: true,
# #         else: Transaction.reference_match?(a_txn, b_txn, nil)

# #     # Only require transaction_type match if both have a type
# #     transaction_type_match =
# #       if is_nil(a_txn.transaction_type) and is_nil(b_txn.transaction_type), do: true,
# #         else: Transaction.transaction_type_match?(a_txn, b_txn)

# #     # Only require transaction_id match if both have an id
# #     transaction_id_match =
# #       if is_nil(a_txn.transaction_id) and is_nil(b_txn.transaction_id), do: true,
# #         else: Transaction.transaction_id_match?(a_txn, b_txn)

# #     # Only require account match if both have an account
# #     account_match =
# #       if is_nil(a_txn.account) and is_nil(b_txn.account), do: true,
# #         else: Transaction.account_match?(a_txn, b_txn)

# #     exact_match = amount_match and date_match and reference_match and
# #                   transaction_type_match and transaction_id_match and account_match

# #     if not exact_match do
# #       Logger.debug("No exact match A(", a_txn.id, ") <-> B(", b_txn.id, "): amount=", amount_match, ", date=", date_match, ", ref=", reference_match, ", type=", transaction_type_match, ", txn_id=", transaction_id_match, ", account=", account_match)
# #     end

# #     exact_match
# #   end

# #   # Build exact match criteria (only include criteria that actually match)
# #   defp build_exact_match_criteria(a_txn, b_txn, settings) do
# #     criteria = []

# #     criteria = if Transaction.amount_match?(a_txn, b_txn, settings.amount_tolerance) do
# #       ["amount" | criteria]
# #     else
# #       criteria
# #     end

# #     criteria = if Transaction.date_match?(a_txn, b_txn, settings.date_tolerance_days) do
# #       ["date" | criteria]
# #     else
# #       criteria
# #     end

# #     criteria = if Transaction.reference_match?(a_txn, b_txn, nil) do
# #       ["reference" | criteria]
# #     else
# #       criteria
# #     end

# #     criteria = if Transaction.transaction_type_match?(a_txn, b_txn) do
# #       ["transaction_type" | criteria]
# #     else
# #       criteria
# #     end

# #     criteria = if Transaction.transaction_id_match?(a_txn, b_txn) do
# #       ["transaction_id" | criteria]
# #     else
# #       criteria
# #     end

# #     criteria = if Transaction.account_match?(a_txn, b_txn) do
# #       ["account" | criteria]
# #     else
# #       criteria
# #     end

# #     Enum.reverse(criteria)
# #   end

# #   # Build list of matching criteria for a pair of transactions
# #   defp build_matching_criteria(a_txn, b_txn, settings) do
# #     criteria = []

# #     criteria = if Transaction.amount_match?(a_txn, b_txn, settings.amount_tolerance) do
# #       ["amount" | criteria]
# #     else
# #       criteria
# #     end

# #     criteria = if Transaction.date_match?(a_txn, b_txn, settings.date_tolerance_days) do
# #       ["date" | criteria]
# #     else
# #       criteria
# #     end

# #     criteria = if Transaction.reference_match?(a_txn, b_txn, nil) do
# #       ["reference" | criteria]
# #     else
# #       criteria
# #     end

# #     criteria = if Transaction.transaction_type_match?(a_txn, b_txn) do
# #       ["transaction_type" | criteria]
# #     else
# #       criteria
# #     end

# #     criteria = if Transaction.transaction_id_match?(a_txn, b_txn) do
# #       ["transaction_id" | criteria]
# #     else
# #       criteria
# #     end

# #     criteria = if Transaction.account_match?(a_txn, b_txn) do
# #       ["account" | criteria]
# #     else
# #       criteria
# #     end

# #     # Check description similarity
# #     criteria = if description_similar?(a_txn.description, b_txn.description) do
# #       ["description" | criteria]
# #     else
# #       criteria
# #     end

# #     Enum.reverse(criteria)
# #   end

# #   # Check if descriptions are similar
# #   defp description_similar?(nil, _), do: false
# #   defp description_similar?(_, nil), do: false
# #   defp description_similar?(desc1, desc2) when desc1 == desc2, do: true
# #   defp description_similar?(desc1, desc2) do
# #     # Simple word-based similarity check
# #     words1 = String.split(String.downcase(desc1))
# #     words2 = String.split(String.downcase(desc2))

# #     common_words = MapSet.intersection(MapSet.new(words1), MapSet.new(words2))
# #     total_words = MapSet.union(MapSet.new(words1), MapSet.new(words2))

# #     if MapSet.size(total_words) > 0 do
# #       similarity = MapSet.size(common_words) / MapSet.size(total_words)
# #       similarity > 0.6  # 60% similarity threshold
# #     else
# #       false
# #     end
# #   end

# #   # Ensure one-to-one matching (no transaction appears in multiple matches)
# #   defp ensure_one_to_one_matching(matches) do
# #     # Sort by confidence score (highest first)
# #     sorted_matches = Enum.sort_by(matches, & Decimal.to_float(&1.confidence_score), :desc)

# #     {final_matches, _, _} = Enum.reduce(sorted_matches, {[], MapSet.new(), MapSet.new()},
# #       fn match, {acc_matches, used_a_ids, used_b_ids} ->
# #         a_id = match.transaction_a.id
# #         b_id = match.transaction_b.id

# #         if MapSet.member?(used_a_ids, a_id) or MapSet.member?(used_b_ids, b_id) do
# #           # Skip this match as one of the transactions is already matched
# #           {acc_matches, used_a_ids, used_b_ids}
# #         else
# #           # Add this match
# #           {[match | acc_matches], MapSet.put(used_a_ids, a_id), MapSet.put(used_b_ids, b_id)}
# #         end
# #       end)

# #     Enum.reverse(final_matches)
# #   end

# #   # Create match records in database and update transaction statuses
# #   defp create_matches_and_update_transactions(matches, reconciliation_run_id) do
# #     Repo.transaction(fn ->
# #       matched_a_ids = Enum.map(matches, & &1.transaction_a.id) |> MapSet.new()
# #       matched_b_ids = Enum.map(matches, & &1.transaction_b.id) |> MapSet.new()

# #       Enum.each(matches, fn match ->
# #         # Create transaction match record
# #         match_attrs = %{
# #           reconciliation_run_id: reconciliation_run_id,
# #           transaction_a_id: match.transaction_a.id,
# #           transaction_b_id: match.transaction_b.id,
# #           match_type: match.match_type,
# #           confidence_score: match.confidence_score,
# #           amount_difference: TransactionMatch.calculate_amount_difference(
# #             match.transaction_a,
# #             match.transaction_b
# #           ),
# #           date_difference_days: TransactionMatch.calculate_date_difference(
# #             match.transaction_a,
# #             match.transaction_b
# #           ),
# #           matching_criteria: match.matching_criteria
# #         }

# #         case Reconciliation.create_transaction_match(match_attrs) do
# #           {:ok, _transaction_match} ->
# #             # Update both transactions as matched
# #             Reconciliation.update_transaction_match_status(match.transaction_a, %{
# #               is_matched: true,
# #               match_confidence: match.confidence_score
# #             })

# #             Reconciliation.update_transaction_match_status(match.transaction_b, %{
# #               is_matched: true,
# #               match_confidence: match.confidence_score
# #             })

# #           {:error, changeset} ->
# #             Logger.error("Failed to create transaction match: #{inspect(changeset.errors)}")
# #         end
# #       end)

# #       # Mark all unmatched transactions as is_matched: false
# #       all_transactions = Reconciliation.get_transactions(reconciliation_run_id)
# #       Enum.each(all_transactions, fn txn ->
# #         if not (MapSet.member?(matched_a_ids, txn.id) or MapSet.member?(matched_b_ids, txn.id)) do
# #           Reconciliation.update_transaction_match_status(txn, %{
# #             is_matched: false,
# #             match_confidence: nil
# #           })
# #         end
# #       end)
# #     end)
# #   end

# #   @doc """
# #   Manually creates a match between two transactions.
# #   """
# #   def create_manual_match(transaction_a_id, transaction_b_id, reconciliation_run_id, notes \\ nil) do
# #     # Get transactions
# #     transaction_a = Repo.get!(Transaction, transaction_a_id)
# #     transaction_b = Repo.get!(Transaction, transaction_b_id)

# #     # Verify they belong to different files
# #     if transaction_a.uploaded_file.file_type == transaction_b.uploaded_file.file_type do
# #       {:error, "Cannot match transactions from the same file"}
# #     else
# #       Repo.transaction(fn ->
# #         # Create manual match
# #         match_changeset = TransactionMatch.create_manual_match(
# #           transaction_a,
# #           transaction_b,
# #           reconciliation_run_id,
# #           notes
# #         )

# #         case Repo.insert(match_changeset) do
# #           {:ok, match} ->
# #             # Update transaction statuses
# #             Reconciliation.update_transaction_match_status(transaction_a, %{
# #               is_matched: true,
# #               match_confidence: Decimal.new("100")
# #             })

# #             Reconciliation.update_transaction_match_status(transaction_b, %{
# #               is_matched: true,
# #               match_confidence: Decimal.new("100")
# #             })

# #             # Recalculate reconciliation stats
# #             Reconciliation.calculate_reconciliation_stats(reconciliation_run_id)

# #             {:ok, match}

# #           {:error, changeset} ->
# #             {:error, changeset}
# #         end
# #       end)
# #     end
# #   end

# #   @doc """
# #   Removes a match between two transactions.
# #   """
# #   def remove_match(match_id) do
# #     match = Repo.get!(TransactionMatch, match_id)
# #     |> Repo.preload([:transaction_a, :transaction_b])

# #     Repo.transaction(fn ->
# #       # Update transaction statuses
# #       Reconciliation.update_transaction_match_status(match.transaction_a, %{
# #         is_matched: false,
# #         match_confidence: nil
# #       })

# #       Reconciliation.update_transaction_match_status(match.transaction_b, %{
# #         is_matched: false,
# #         match_confidence: nil
# #       })

# #       # Delete the match
# #       Repo.delete!(match)

# #       # Recalculate reconciliation stats
# #       Reconciliation.calculate_reconciliation_stats(match.reconciliation_run_id)
# #     end)
# #   end
# # end
# defmodule Reconciliation.Services.MatchingEngine do
#   @moduledoc """
#   Service for matching transactions between two files in a reconciliation run.
#   """

#   require Logger

#   alias Reconciliation.{
#     Repo,
#     Transaction,
#     TransactionMatch,
#     ReconciliationRun
#   }

#   require Logger

#   @doc """
#   Performs transaction matching for a reconciliation run.
#   """
#   def match_transactions(%ReconciliationRun{} = run) do
#     Logger.info("Starting transaction matching for run #{run.id}")

#     # Get user settings
#     {:ok, settings} = Reconciliation.get_or_create_settings(run.user_id)

#     # Get transactions from both files
#     transactions = Reconciliation.get_transactions(run.id)

#     file_a_transactions = Enum.filter(transactions, fn t ->
#       t.uploaded_file.file_type == "file_a"
#     end)

#     file_b_transactions = Enum.filter(transactions, fn t ->
#       t.uploaded_file.file_type == "file_b"
#     end)

#     Logger.info("Found #{length(file_a_transactions)} transactions in File A, #{length(file_b_transactions)} in File B")

#     # Perform matching
#     matches = find_matches(file_a_transactions, file_b_transactions, settings)

#     # Create match records and update transaction statuses
#     create_matches_and_update_transactions(matches, run.id)

#     # Update reconciliation run statistics
#     Reconciliation.calculate_reconciliation_stats(run.id)

#     Logger.info("Completed transaction matching for run #{run.id}")
#     {:ok, matches}
#   end

#   # Find matches between transactions from file A and file B
#   defp find_matches(file_a_transactions, file_b_transactions, settings) do
#     Logger.info("Auto-match settings: exact=#{settings.auto_match_exact}, fuzzy=#{settings.auto_match_fuzzy}")

#     # First pass: Find exact matches if enabled
#     {exact_matches, remaining_a, remaining_b} = if settings.auto_match_exact do
#       find_exact_matches(file_a_transactions, file_b_transactions, settings)
#     else
#       Logger.info("Auto exact matching is disabled")
#       {[], file_a_transactions, file_b_transactions}
#     end

#     matched_b_ids = exact_matches
#     |> Enum.map(& &1.transaction_b.id)
#     |> MapSet.new()

#     # Second pass: Find fuzzy matches if enabled
#     fuzzy_matches = if settings.auto_match_fuzzy do
#       find_fuzzy_matches(remaining_a, remaining_b, matched_b_ids, settings)
#     else
#       Logger.info("Auto fuzzy matching is disabled")
#       []
#     end

#     exact_matches ++ fuzzy_matches
#   end

#   # Find exact matches based on amount, date, and reference
#   defp find_exact_matches(file_a_transactions, file_b_transactions, settings) do
#     Logger.info("Finding exact matches: #{length(file_a_transactions)} A transactions, #{length(file_b_transactions)} B transactions")

#     matches = for a_txn <- file_a_transactions,
#                   b_txn <- file_b_transactions do
#       case is_exact_match?(a_txn, b_txn, settings) do
#         {:match, criteria} ->
#           Logger.debug("Found exact match: A(#{a_txn.id}) <-> B(#{b_txn.id}) - Criteria: #{inspect(criteria)}")
#           %{
#             transaction_a: a_txn,
#             transaction_b: b_txn,
#             match_type: "exact",
#             confidence_score: Decimal.new("100"),
#             matching_criteria: criteria
#           }
#         {:no_match, reason} ->
#           Logger.debug("No match A(#{a_txn.id}) <-> B(#{b_txn.id}): #{reason}")
#           nil
#         {:error, error} ->
#           Logger.warn("Error matching A(#{a_txn.id}) <-> B(#{b_txn.id}): #{error}")
#           nil
#       end
#     end
#     |> Enum.reject(&is_nil/1)

#     Logger.info("Found #{length(matches)} exact matches before deduplication")

#     # Remove duplicates and ensure one-to-one matching
#     unique_matches = ensure_one_to_one_matching(matches)

#     Logger.info("After deduplication: #{length(unique_matches)} unique exact matches")

#     matched_a_ids = unique_matches |> Enum.map(& &1.transaction_a.id) |> MapSet.new()
#     matched_b_ids = unique_matches |> Enum.map(& &1.transaction_b.id) |> MapSet.new()

#     remaining_a = Enum.reject(file_a_transactions, &MapSet.member?(matched_a_ids, &1.id))
#     remaining_b = Enum.reject(file_b_transactions, &MapSet.member?(matched_b_ids, &1.id))

#     Logger.info("Remaining unmatched: #{length(remaining_a)} A transactions, #{length(remaining_b)} B transactions")

#     {unique_matches, remaining_a, remaining_b}
#   end

#   # Find fuzzy matches based on similarity scores
#   defp find_fuzzy_matches(file_a_transactions, file_b_transactions, already_matched_b_ids, settings) do
#     threshold = Decimal.to_float(settings.fuzzy_match_threshold)

#     matches = for a_txn <- file_a_transactions,
#                   b_txn <- file_b_transactions,
#                   not MapSet.member?(already_matched_b_ids, b_txn.id) do

#       try do
#         similarity = Transaction.similarity_score(a_txn, b_txn, [
#           amount_tolerance: settings.amount_tolerance,
#           date_tolerance: settings.date_tolerance_days
#         ])

#         if similarity >= threshold * 100 do
#           criteria = build_matching_criteria(a_txn, b_txn, settings)

#           %{
#             transaction_a: a_txn,
#             transaction_b: b_txn,
#             match_type: "fuzzy",
#             confidence_score: Decimal.from_float(similarity),
#             matching_criteria: criteria
#           }
#         else
#           nil
#         end
#       rescue
#         error ->
#           Logger.warn("Error calculating similarity for A(#{a_txn.id}) <-> B(#{b_txn.id}): #{inspect(error)}")
#           nil
#       end
#     end
#     |> Enum.reject(&is_nil/1)
#     |> Enum.sort_by(& Decimal.to_float(&1.confidence_score), :desc)

#     # Ensure one-to-one matching for fuzzy matches too
#     ensure_one_to_one_matching(matches)
#   end

#   # Improved exact match function that returns detailed results
#   defp is_exact_match?(a_txn, b_txn, settings) do
#     try do
#       # Collect all match results with detailed information
#       match_results = %{
#         amount: safe_field_match(&Transaction.amount_match?/3, a_txn, b_txn, settings.amount_tolerance),
#         date: safe_field_match(&Transaction.date_match?/3, a_txn, b_txn, settings.date_tolerance_days),
#         reference: safe_optional_field_match(&Transaction.reference_match?/3, a_txn, b_txn, nil, :reference),
#         transaction_type: safe_optional_field_match(&Transaction.transaction_type_match?/2, a_txn, b_txn, nil, :transaction_type),
#         transaction_id: safe_optional_field_match(&Transaction.transaction_id_match?/2, a_txn, b_txn, nil, :transaction_id),
#         account: safe_optional_field_match(&Transaction.account_match?/2, a_txn, b_txn, nil, :account)
#       }

#       # Separate required fields from optional fields
#       required_matches = [:amount, :date]
#       optional_matches = [:reference, :transaction_type, :transaction_id, :account]

#       # Check if all required fields match
#       required_results = Map.take(match_results, required_matches)
#       failed_required = Enum.filter(required_results, fn {_field, result} ->
#         result != :match
#       end)

#       if Enum.empty?(failed_required) do
#         # All required fields match, now check optional fields
#         optional_results = Map.take(match_results, optional_matches)

#         # For optional fields, we only fail if there's an explicit mismatch
#         # (not if fields are missing)
#         failed_optional = Enum.filter(optional_results, fn {_field, result} ->
#           result == :mismatch
#         end)

#         if Enum.empty?(failed_optional) do
#           # Build criteria list of matching fields
#           matching_criteria =
#             match_results
#             |> Enum.filter(fn {_field, result} -> result == :match end)
#             |> Enum.map(fn {field, _result} -> Atom.to_string(field) end)

#           {:match, matching_criteria}
#         else
#           failed_fields = Enum.map(failed_optional, fn {field, _} -> field end)
#           {:no_match, "Optional field mismatch: #{inspect(failed_fields)}"}
#         end
#       else
#         failed_fields = Enum.map(failed_required, fn {field, _} -> field end)
#         {:no_match, "Required field mismatch: #{inspect(failed_fields)}"}
#       end
#     rescue
#       error ->
#         {:error, "Exception during matching: #{inspect(error)}"}
#     end
#   end

#   # Safe wrapper for required field matching
#   defp safe_field_match(match_func, a_txn, b_txn, tolerance) do
#     try do
#       case match_func.(a_txn, b_txn, tolerance) do
#         true -> :match
#         false -> :mismatch
#         nil -> :missing_data
#       end
#     rescue
#       error ->
#         Logger.warn("Error in field match: #{inspect(error)}")
#         :error
#     end
#   end

#   # Safe wrapper for optional field matching (2-arity functions)
#   defp safe_optional_field_match(match_func, a_txn, b_txn, _tolerance, field_name) when is_function(match_func, 2) do
#     try do
#       # Check if both fields are nil - if so, consider it a non-applicable match
#       a_val = Map.get(a_txn, field_name)
#       b_val = Map.get(b_txn, field_name)

#       cond do
#         is_nil(a_val) and is_nil(b_val) -> :not_applicable
#         is_nil(a_val) or is_nil(b_val) -> :missing_data
#         true ->
#           case match_func.(a_txn, b_txn) do
#             true -> :match
#             false -> :mismatch
#             nil -> :missing_data
#           end
#       end
#     rescue
#       error ->
#         Logger.warn("Error in optional field match for #{field_name}: #{inspect(error)}")
#         :error
#     end
#   end

#   # Safe wrapper for optional field matching (3-arity functions)
#   defp safe_optional_field_match(match_func, a_txn, b_txn, tolerance, field_name) when is_function(match_func, 3) do
#     try do
#       # Check if both fields are nil - if so, consider it a non-applicable match
#       a_val = Map.get(a_txn, field_name)
#       b_val = Map.get(b_txn, field_name)

#       cond do
#         is_nil(a_val) and is_nil(b_val) -> :not_applicable
#         is_nil(a_val) or is_nil(b_val) -> :missing_data
#         true ->
#           case match_func.(a_txn, b_txn, tolerance) do
#             true -> :match
#             false -> :mismatch
#             nil -> :missing_data
#           end
#       end
#     rescue
#       error ->
#         Logger.warn("Error in optional field match for #{field_name}: #{inspect(error)}")
#         :error
#     end
#   end

#   # Build list of matching criteria for a pair of transactions
#   defp build_matching_criteria(a_txn, b_txn, settings) do
#     criteria = []

#     criteria = if safe_amount_match?(a_txn, b_txn, settings.amount_tolerance) do
#       ["amount" | criteria]
#     else
#       criteria
#     end

#     criteria = if safe_date_match?(a_txn, b_txn, settings.date_tolerance_days) do
#       ["date" | criteria]
#     else
#       criteria
#     end

#     criteria = if safe_reference_match?(a_txn, b_txn) do
#       ["reference" | criteria]
#     else
#       criteria
#     end

#     criteria = if safe_transaction_type_match?(a_txn, b_txn) do
#       ["transaction_type" | criteria]
#     else
#       criteria
#     end

#     criteria = if safe_transaction_id_match?(a_txn, b_txn) do
#       ["transaction_id" | criteria]
#     else
#       criteria
#     end

#     criteria = if safe_account_match?(a_txn, b_txn) do
#       ["account" | criteria]
#     else
#       criteria
#     end

#     # Check description similarity
#     criteria = if description_similar?(a_txn.description, b_txn.description) do
#       ["description" | criteria]
#     else
#       criteria
#     end

#     Enum.reverse(criteria)
#   end

#   # Safe wrapper functions for individual field checks
#   defp safe_amount_match?(a_txn, b_txn, tolerance) do
#     try do
#       Transaction.amount_match?(a_txn, b_txn, tolerance)
#     rescue
#       _ -> false
#     end
#   end

#   defp safe_date_match?(a_txn, b_txn, tolerance) do
#     try do
#       Transaction.date_match?(a_txn, b_txn, tolerance)
#     rescue
#       _ -> false
#     end
#   end

#   defp safe_reference_match?(a_txn, b_txn) do
#     try do
#       # Only match if both have references
#       if is_nil(a_txn.reference) or is_nil(b_txn.reference) do
#         false
#       else
#         Transaction.reference_match?(a_txn, b_txn, nil)
#       end
#     rescue
#       _ -> false
#     end
#   end

#   defp safe_transaction_type_match?(a_txn, b_txn) do
#     try do
#       # Only match if both have transaction types
#       if is_nil(a_txn.transaction_type) or is_nil(b_txn.transaction_type) do
#         false
#       else
#         Transaction.transaction_type_match?(a_txn, b_txn)
#       end
#     rescue
#       _ -> false
#     end
#   end

#   defp safe_transaction_id_match?(a_txn, b_txn) do
#     try do
#       # Only match if both have transaction IDs
#       if is_nil(a_txn.transaction_id) or is_nil(b_txn.transaction_id) do
#         false
#       else
#         Transaction.transaction_id_match?(a_txn, b_txn)
#       end
#     rescue
#       _ -> false
#     end
#   end

#   defp safe_account_match?(a_txn, b_txn) do
#     try do
#       # Only match if both have accounts
#       if is_nil(a_txn.account) or is_nil(b_txn.account) do
#         false
#       else
#         Transaction.account_match?(a_txn, b_txn)
#       end
#     rescue
#       _ -> false
#     end
#   end

#   # Check if descriptions are similar
#   defp description_similar?(nil, _), do: false
#   defp description_similar?(_, nil), do: false
#   defp description_similar?(desc1, desc2) when desc1 == desc2, do: true
#   defp description_similar?(desc1, desc2) do
#     try do
#       # Simple word-based similarity check
#       words1 = String.split(String.downcase(desc1))
#       words2 = String.split(String.downcase(desc2))

#       common_words = MapSet.intersection(MapSet.new(words1), MapSet.new(words2))
#       total_words = MapSet.union(MapSet.new(words1), MapSet.new(words2))

#       if MapSet.size(total_words) > 0 do
#         similarity = MapSet.size(common_words) / MapSet.size(total_words)
#         similarity > 0.6  # 60% similarity threshold
#       else
#         false
#       end
#     rescue
#       _ -> false
#     end
#   end

#   # Ensure one-to-one matching (no transaction appears in multiple matches)
#   defp ensure_one_to_one_matching(matches) do
#     # Sort by confidence score (highest first)
#     sorted_matches = Enum.sort_by(matches, & Decimal.to_float(&1.confidence_score), :desc)

#     {final_matches, _, _} = Enum.reduce(sorted_matches, {[], MapSet.new(), MapSet.new()},
#       fn match, {acc_matches, used_a_ids, used_b_ids} ->
#         a_id = match.transaction_a.id
#         b_id = match.transaction_b.id

#         if MapSet.member?(used_a_ids, a_id) or MapSet.member?(used_b_ids, b_id) do
#           # Skip this match as one of the transactions is already matched
#           {acc_matches, used_a_ids, used_b_ids}
#         else
#           # Add this match
#           {[match | acc_matches], MapSet.put(used_a_ids, a_id), MapSet.put(used_b_ids, b_id)}
#         end
#       end)

#     Enum.reverse(final_matches)
#   end

#   # Create match records in database and update transaction statuses
#   defp create_matches_and_update_transactions(matches, reconciliation_run_id) do
#     Repo.transaction(fn ->
#       matched_a_ids = Enum.map(matches, & &1.transaction_a.id) |> MapSet.new()
#       matched_b_ids = Enum.map(matches, & &1.transaction_b.id) |> MapSet.new()

#       Enum.each(matches, fn match ->
#         try do
#           # Create transaction match record
#           match_attrs = %{
#             reconciliation_run_id: reconciliation_run_id,
#             transaction_a_id: match.transaction_a.id,
#             transaction_b_id: match.transaction_b.id,
#             match_type: match.match_type,
#             confidence_score: match.confidence_score,
#             amount_difference: TransactionMatch.calculate_amount_difference(
#               match.transaction_a,
#               match.transaction_b
#             ),
#             date_difference_days: TransactionMatch.calculate_date_difference(
#               match.transaction_a,
#               match.transaction_b
#             ),
#             matching_criteria: match.matching_criteria
#           }

#           case Reconciliation.create_transaction_match(match_attrs) do
#             {:ok, _transaction_match} ->
#               # Update both transactions as matched
#               Reconciliation.update_transaction_match_status(match.transaction_a, %{
#                 is_matched: true,
#                 match_confidence: match.confidence_score
#               })

#               Reconciliation.update_transaction_match_status(match.transaction_b, %{
#                 is_matched: true,
#                 match_confidence: match.confidence_score
#               })

#             {:error, changeset} ->
#               Logger.error("Failed to create transaction match for A(#{match.transaction_a.id}) <-> B(#{match.transaction_b.id}): #{inspect(changeset.errors)}")
#           end
#         rescue
#           error ->
#             Logger.error("Exception creating match for A(#{match.transaction_a.id}) <-> B(#{match.transaction_b.id}): #{inspect(error)}")
#         end
#       end)

#       # Mark all unmatched transactions as is_matched: false
#       try do
#         all_transactions = Reconciliation.get_transactions(reconciliation_run_id)
#         Enum.each(all_transactions, fn txn ->
#           if not (MapSet.member?(matched_a_ids, txn.id) or MapSet.member?(matched_b_ids, txn.id)) do
#             Reconciliation.update_transaction_match_status(txn, %{
#               is_matched: false,
#               match_confidence: nil
#             })
#           end
#         end)
#       rescue
#         error ->
#           Logger.error("Error updating unmatched transaction statuses: #{inspect(error)}")
#       end
#     end)
#   end

#   @doc """
#   Manually creates a match between two transactions.
#   """
#   def create_manual_match(transaction_a_id, transaction_b_id, reconciliation_run_id, notes \\ nil) do
#     try do
#       # Get transactions
#       transaction_a = Repo.get!(Transaction, transaction_a_id)
#       transaction_b = Repo.get!(Transaction, transaction_b_id)

#       # Verify they belong to different files
#       if transaction_a.uploaded_file.file_type == transaction_b.uploaded_file.file_type do
#         {:error, "Cannot match transactions from the same file"}
#       else
#         Repo.transaction(fn ->
#           # Create manual match
#           match_changeset = TransactionMatch.create_manual_match(
#             transaction_a,
#             transaction_b,
#             reconciliation_run_id,
#             notes
#           )

#           case Repo.insert(match_changeset) do
#             {:ok, match} ->
#               # Update transaction statuses
#               Reconciliation.update_transaction_match_status(transaction_a, %{
#                 is_matched: true,
#                 match_confidence: Decimal.new("100")
#               })

#               Reconciliation.update_transaction_match_status(transaction_b, %{
#                 is_matched: true,
#                 match_confidence: Decimal.new("100")
#               })

#               # Recalculate reconciliation stats
#               Reconciliation.calculate_reconciliation_stats(reconciliation_run_id)

#               {:ok, match}

#             {:error, changeset} ->
#               {:error, changeset}
#           end
#         end)
#       end
#     rescue
#       error ->
#         Logger.error("Exception in create_manual_match: #{inspect(error)}")
#         {:error, "Failed to create manual match: #{inspect(error)}"}
#     end
#   end

#   @doc """
#   Removes a match between two transactions.
#   """
#   def remove_match(match_id) do
#     try do
#       match = Repo.get!(TransactionMatch, match_id)
#       |> Repo.preload([:transaction_a, :transaction_b])

#       Repo.transaction(fn ->
#         # Update transaction statuses
#         Reconciliation.update_transaction_match_status(match.transaction_a, %{
#           is_matched: false,
#           match_confidence: nil
#         })

#         Reconciliation.update_transaction_match_status(match.transaction_b, %{
#           is_matched: false,
#           match_confidence: nil
#         })

#         # Delete the match
#         Repo.delete!(match)

#         # Recalculate reconciliation stats
#         Reconciliation.calculate_reconciliation_stats(match.reconciliation_run_id)
#       end)
#     rescue
#       error ->
#         Logger.error("Exception in remove_match: #{inspect(error)}")
#         {:error, "Failed to remove match: #{inspect(error)}"}
#     end
#   end
# end
defmodule Reconciliation.Services.MatchingEngine do
  @moduledoc """
  Service for matching transactions between two files in a reconciliation run.
  """

  require Logger

  alias Reconciliation.{
    Repo,
    Transaction,
    TransactionMatch,
    ReconciliationRun
  }

  require Logger

  @doc """
  Performs transaction matching for a reconciliation run.
  """
  def match_transactions(%ReconciliationRun{} = run) do
    Logger.info("Starting transaction matching for run #{run.id}")

    # Get user settings
    {:ok, settings} = Reconciliation.get_or_create_settings(run.user_id)

    # Get transactions from both files
    transactions = Reconciliation.get_transactions(run.id)

    # Ensure we have transactions from both files
    file_a_transactions = Enum.filter(transactions, fn t ->
      t.uploaded_file.file_type == "file_a"
    end)

    file_b_transactions = Enum.filter(transactions, fn t ->
      t.uploaded_file.file_type == "file_b"
    end)

    Logger.info("Found #{length(file_a_transactions)} transactions in File A, #{length(file_b_transactions)} in File B")
    Logger.info("Total transactions to process: #{length(transactions)}")

    # Initialize all transactions with unmatched status at the start
    # This ensures every transaction gets a status, even if matching fails
    Logger.info("Pre-initializing all transaction match statuses")
    Enum.each(transactions, fn txn ->
      try do
        Reconciliation.update_transaction_match_status(txn, %{
          is_matched: false,
          match_confidence: nil
        })
      rescue
        error ->
          Logger.warn("Error pre-initializing transaction #{txn.id}: #{inspect(error)}")
      end
    end)

    # Perform matching
    matches = find_matches(file_a_transactions, file_b_transactions, settings)

    # Create match records and update transaction statuses
    create_matches_and_update_transactions(matches, run.id)

    # Update reconciliation run statistics
    Reconciliation.calculate_reconciliation_stats(run.id)

    Logger.info("Completed transaction matching for run #{run.id}")
    {:ok, matches}
  end

  # Find matches between transactions from file A and file B
  defp find_matches(file_a_transactions, file_b_transactions, settings) do
    Logger.info("Auto-match settings: exact=#{settings.auto_match_exact}, fuzzy=#{settings.auto_match_fuzzy}")

    # First pass: Find exact matches if enabled
    {exact_matches, remaining_a, remaining_b} = if settings.auto_match_exact do
      find_exact_matches(file_a_transactions, file_b_transactions, settings)
    else
      Logger.info("Auto exact matching is disabled")
      {[], file_a_transactions, file_b_transactions}
    end

    matched_b_ids = exact_matches
    |> Enum.map(& &1.transaction_b.id)
    |> MapSet.new()

    # Ultra-strict mode: NO fuzzy matching allowed - only exact matches
    Logger.info("Ultra-strict matching mode: Fuzzy matching is completely disabled")
    Logger.info("Only transactions with ALL fields matching exactly will be paired")

    exact_matches
  end

  # Find exact matches based on amount, date, and reference
  defp find_exact_matches(file_a_transactions, file_b_transactions, settings) do
    Logger.info("Finding exact matches: #{length(file_a_transactions)} A transactions, #{length(file_b_transactions)} B transactions")

    matches = for a_txn <- file_a_transactions,
                  b_txn <- file_b_transactions do
      case is_exact_match?(a_txn, b_txn, settings) do
        {:match, criteria} ->
          Logger.debug("Found exact match: A(#{a_txn.id}) <-> B(#{b_txn.id}) - Criteria: #{inspect(criteria)}")
          %{
            transaction_a: a_txn,
            transaction_b: b_txn,
            match_type: "exact",
            confidence_score: Decimal.new("100"),
            matching_criteria: criteria
          }
        {:no_match, reason} ->
          Logger.debug("No match A(#{a_txn.id}) <-> B(#{b_txn.id}): #{reason}")
          nil
        {:error, error} ->
          Logger.warn("Error matching A(#{a_txn.id}) <-> B(#{b_txn.id}): #{error}")
          nil
      end
    end
    |> Enum.reject(&is_nil/1)

    Logger.info("Found #{length(matches)} exact matches before deduplication")

    # Remove duplicates and ensure one-to-one matching
    unique_matches = ensure_one_to_one_matching(matches)

    Logger.info("After deduplication: #{length(unique_matches)} unique exact matches")

    matched_a_ids = unique_matches |> Enum.map(& &1.transaction_a.id) |> MapSet.new()
    matched_b_ids = unique_matches |> Enum.map(& &1.transaction_b.id) |> MapSet.new()

    remaining_a = Enum.reject(file_a_transactions, &MapSet.member?(matched_a_ids, &1.id))
    remaining_b = Enum.reject(file_b_transactions, &MapSet.member?(matched_b_ids, &1.id))

    Logger.info("Remaining unmatched: #{length(remaining_a)} A transactions, #{length(remaining_b)} B transactions")

    {unique_matches, remaining_a, remaining_b}
  end

  # Find fuzzy matches based on similarity scores
  defp find_fuzzy_matches(file_a_transactions, file_b_transactions, already_matched_b_ids, settings) do
    threshold = Decimal.to_float(settings.fuzzy_match_threshold)

    matches = for a_txn <- file_a_transactions,
                  b_txn <- file_b_transactions,
                  not MapSet.member?(already_matched_b_ids, b_txn.id) do

      try do
        similarity = Transaction.similarity_score(a_txn, b_txn, [
          amount_tolerance: settings.amount_tolerance,
          date_tolerance: settings.date_tolerance_days
        ])

        if similarity >= threshold * 100 do
          criteria = build_matching_criteria(a_txn, b_txn, settings)

          %{
            transaction_a: a_txn,
            transaction_b: b_txn,
            match_type: "fuzzy",
            confidence_score: Decimal.from_float(similarity),
            matching_criteria: criteria
          }
        else
          nil
        end
      rescue
        error ->
          Logger.warn("Error calculating similarity for A(#{a_txn.id}) <-> B(#{b_txn.id}): #{inspect(error)}")
          nil
      end
    end
    |> Enum.reject(&is_nil/1)
    |> Enum.sort_by(& Decimal.to_float(&1.confidence_score), :desc)

    # Ensure one-to-one matching for fuzzy matches too
    ensure_one_to_one_matching(matches)
  end

  # Ultra-strict exact match function that requires ALL fields to match exactly
  defp is_exact_match?(a_txn, b_txn, _settings) do
    try do
      # Collect all match results with ZERO tolerance for any differences
      match_results = %{
        amount: ultra_strict_field_match(&Transaction.amount_match?/3, a_txn, b_txn, Decimal.new("0"), :amount),
        date: ultra_strict_field_match(&Transaction.date_match?/3, a_txn, b_txn, 0, :transaction_date),
        reference: ultra_strict_field_match(&Transaction.reference_match?/3, a_txn, b_txn, nil, :reference),
        transaction_type: ultra_strict_field_match(&Transaction.transaction_type_match?/2, a_txn, b_txn, nil, :transaction_type),
        transaction_id: ultra_strict_field_match(&Transaction.transaction_id_match?/2, a_txn, b_txn, nil, :transaction_id),
        account: ultra_strict_field_match(&Transaction.account_match?/2, a_txn, b_txn, nil, :account),
        description: ultra_strict_description_match(a_txn, b_txn)
      }

      # For ultra-strict matching, ALL fields must match exactly - no exceptions
      # Even nil vs nil is not acceptable unless both transactions have identical field states
      failed_fields = Enum.filter(match_results, fn {_field, result} ->
        result != :match
      end)

      if Enum.empty?(failed_fields) do
        # Build criteria list of ALL matching fields (ultra-strict requires ALL)
        matching_criteria =
          match_results
          |> Enum.filter(fn {_field, result} -> result == :match end)
          |> Enum.map(fn {field, _result} -> Atom.to_string(field) end)

        # Ultra-strict mode requires ALL 7 fields to match
        required_fields = ["amount", "date", "reference", "transaction_type", "transaction_id", "account", "description"]
        if length(matching_criteria) == length(required_fields) do
          {:match, matching_criteria}
        else
          missing_fields = required_fields -- matching_criteria
          {:no_match, "Ultra-strict mode: Missing exact matches for fields: #{inspect(missing_fields)}"}
        end
      else
        failed_field_names = Enum.map(failed_fields, fn {field, result} ->
          "#{field}(#{result})"
        end)
        {:no_match, "Ultra-strict mode: Field requirements not met: #{inspect(failed_field_names)}"}
      end
    rescue
      error ->
        {:error, "Exception during matching: #{inspect(error)}"}
    end
  end

  # Safe wrapper for required field matching
  defp safe_field_match(match_func, a_txn, b_txn, tolerance) do
    try do
      case match_func.(a_txn, b_txn, tolerance) do
        true -> :match
        false -> :mismatch
        nil -> :missing_data
      end
    rescue
      error ->
        Logger.warn("Error in field match: #{inspect(error)}")
        :error
    end
  end

  # Safe wrapper for optional field matching (2-arity functions)
  defp safe_optional_field_match(match_func, a_txn, b_txn, _tolerance, field_name) when is_function(match_func, 2) do
    try do
      # Check if both fields are nil - if so, consider it a non-applicable match
      a_val = Map.get(a_txn, field_name)
      b_val = Map.get(b_txn, field_name)

      cond do
        is_nil(a_val) and is_nil(b_val) -> :not_applicable
        is_nil(a_val) or is_nil(b_val) -> :missing_data
        true ->
          case match_func.(a_txn, b_txn) do
            true -> :match
            false -> :mismatch
            nil -> :missing_data
          end
      end
    rescue
      error ->
        Logger.warn("Error in optional field match for #{field_name}: #{inspect(error)}")
        :error
    end
  end

  # Safe wrapper for optional field matching (3-arity functions)
  defp safe_optional_field_match(match_func, a_txn, b_txn, tolerance, field_name) when is_function(match_func, 3) do
    try do
      # Check if both fields are nil - if so, consider it a non-applicable match
      a_val = Map.get(a_txn, field_name)
      b_val = Map.get(b_txn, field_name)

      cond do
        is_nil(a_val) and is_nil(b_val) -> :not_applicable
        is_nil(a_val) or is_nil(b_val) -> :missing_data
        true ->
          case match_func.(a_txn, b_txn, tolerance) do
            true -> :match
            false -> :mismatch
            nil -> :missing_data
          end
      end
    rescue
      error ->
        Logger.warn("Error in optional field match for #{field_name}: #{inspect(error)}")
        :error
    end
  end

  # Safe wrapper for strict field matching (2-arity functions) - requires ALL available fields to match
  defp safe_strict_field_match(match_func, a_txn, b_txn, _tolerance, field_name) when is_function(match_func, 2) do
    try do
      a_val = Map.get(a_txn, field_name)
      b_val = Map.get(b_txn, field_name)

      cond do
        is_nil(a_val) and is_nil(b_val) -> :not_applicable
        is_nil(a_val) or is_nil(b_val) -> :missing_data  # Fail if one has data and other doesn't
        true ->
          case match_func.(a_txn, b_txn) do
            true -> :match
            false -> :mismatch
            nil -> :missing_data
          end
      end
    rescue
      error ->
        Logger.warn("Error in strict field match for #{field_name}: #{inspect(error)}")
        :error
    end
  end

  # Safe wrapper for strict field matching (3-arity functions) - requires ALL available fields to match
  defp safe_strict_field_match(match_func, a_txn, b_txn, tolerance, field_name) when is_function(match_func, 3) do
    try do
      a_val = Map.get(a_txn, field_name)
      b_val = Map.get(b_txn, field_name)

      cond do
        is_nil(a_val) and is_nil(b_val) -> :not_applicable
        is_nil(a_val) or is_nil(b_val) -> :missing_data  # Fail if one has data and other doesn't
        true ->
          case match_func.(a_txn, b_txn, tolerance) do
            true -> :match
            false -> :mismatch
            nil -> :missing_data
          end
      end
    rescue
      error ->
        Logger.warn("Error in strict field match for #{field_name}: #{inspect(error)}")
        :error
    end
  end

  # Ultra-strict field matching (2-arity functions) - ALL fields must match exactly, no exceptions
  defp ultra_strict_field_match(match_func, a_txn, b_txn, _tolerance, field_name) when is_function(match_func, 2) do
    try do
      a_val = Map.get(a_txn, field_name)
      b_val = Map.get(b_txn, field_name)

      # Both must have identical values - no nil tolerance
      cond do
        is_nil(a_val) and is_nil(b_val) -> :mismatch  # Both nil = no match
        is_nil(a_val) or is_nil(b_val) -> :mismatch   # One nil = no match
        true ->
          case match_func.(a_txn, b_txn) do
            true -> :match
            false -> :mismatch
            nil -> :mismatch
          end
      end
    rescue
      error ->
        Logger.warn("Error in ultra-strict field match for #{field_name}: #{inspect(error)}")
        :mismatch
    end
  end

  # Ultra-strict field matching (3-arity functions) - ALL fields must match exactly, no exceptions
  defp ultra_strict_field_match(match_func, a_txn, b_txn, tolerance, field_name) when is_function(match_func, 3) do
    try do
      a_val = Map.get(a_txn, field_name)
      b_val = Map.get(b_txn, field_name)

      # Both must have identical values - no nil tolerance
      cond do
        is_nil(a_val) and is_nil(b_val) -> :mismatch  # Both nil = no match
        is_nil(a_val) or is_nil(b_val) -> :mismatch   # One nil = no match
        true ->
          case match_func.(a_txn, b_txn, tolerance) do
            true -> :match
            false -> :mismatch
            nil -> :mismatch
          end
      end
    rescue
      error ->
        Logger.warn("Error in ultra-strict field match for #{field_name}: #{inspect(error)}")
        :mismatch
    end
  end

  # Ultra-strict description matching - must be exactly identical
  defp ultra_strict_description_match(a_txn, b_txn) do
    try do
      a_desc = a_txn.description
      b_desc = b_txn.description

      cond do
        is_nil(a_desc) and is_nil(b_desc) -> :mismatch  # Both nil = no match
        is_nil(a_desc) or is_nil(b_desc) -> :mismatch   # One nil = no match
        String.trim(a_desc) == String.trim(b_desc) -> :match  # Exact match (ignoring whitespace)
        true -> :mismatch
      end
    rescue
      error ->
        Logger.warn("Error in ultra-strict description match: #{inspect(error)}")
        :mismatch
    end
  end

  # Build list of matching criteria for a pair of transactions
  defp build_matching_criteria(a_txn, b_txn, settings) do
    criteria = []

    criteria = if safe_amount_match?(a_txn, b_txn, settings.amount_tolerance) do
      ["amount" | criteria]
    else
      criteria
    end

    criteria = if safe_date_match?(a_txn, b_txn, settings.date_tolerance_days) do
      ["date" | criteria]
    else
      criteria
    end

    criteria = if safe_reference_match?(a_txn, b_txn) do
      ["reference" | criteria]
    else
      criteria
    end

    criteria = if safe_transaction_type_match?(a_txn, b_txn) do
      ["transaction_type" | criteria]
    else
      criteria
    end

    criteria = if safe_transaction_id_match?(a_txn, b_txn) do
      ["transaction_id" | criteria]
    else
      criteria
    end

    criteria = if safe_account_match?(a_txn, b_txn) do
      ["account" | criteria]
    else
      criteria
    end

    # Check description similarity
    criteria = if description_similar?(a_txn.description, b_txn.description) do
      ["description" | criteria]
    else
      criteria
    end

    Enum.reverse(criteria)
  end

  # Safe wrapper functions for individual field checks
  defp safe_amount_match?(a_txn, b_txn, tolerance) do
    try do
      Transaction.amount_match?(a_txn, b_txn, tolerance)
    rescue
      _ -> false
    end
  end

  defp safe_date_match?(a_txn, b_txn, tolerance) do
    try do
      Transaction.date_match?(a_txn, b_txn, tolerance)
    rescue
      _ -> false
    end
  end

  defp safe_reference_match?(a_txn, b_txn) do
    try do
      # Only match if both have references
      if is_nil(a_txn.reference) or is_nil(b_txn.reference) do
        false
      else
        Transaction.reference_match?(a_txn, b_txn, nil)
      end
    rescue
      _ -> false
    end
  end

  defp safe_transaction_type_match?(a_txn, b_txn) do
    try do
      # Only match if both have transaction types
      if is_nil(a_txn.transaction_type) or is_nil(b_txn.transaction_type) do
        false
      else
        Transaction.transaction_type_match?(a_txn, b_txn)
      end
    rescue
      _ -> false
    end
  end

  defp safe_transaction_id_match?(a_txn, b_txn) do
    try do
      # Only match if both have transaction IDs
      if is_nil(a_txn.transaction_id) or is_nil(b_txn.transaction_id) do
        false
      else
        Transaction.transaction_id_match?(a_txn, b_txn)
      end
    rescue
      _ -> false
    end
  end

  defp safe_account_match?(a_txn, b_txn) do
    try do
      # Only match if both have accounts
      if is_nil(a_txn.account) or is_nil(b_txn.account) do
        false
      else
        Transaction.account_match?(a_txn, b_txn)
      end
    rescue
      _ -> false
    end
  end

  # Check if descriptions are similar
  defp description_similar?(nil, _), do: false
  defp description_similar?(_, nil), do: false
  defp description_similar?(desc1, desc2) when desc1 == desc2, do: true
  defp description_similar?(desc1, desc2) do
    try do
      # Simple word-based similarity check
      words1 = String.split(String.downcase(desc1))
      words2 = String.split(String.downcase(desc2))

      common_words = MapSet.intersection(MapSet.new(words1), MapSet.new(words2))
      total_words = MapSet.union(MapSet.new(words1), MapSet.new(words2))

      if MapSet.size(total_words) > 0 do
        similarity = MapSet.size(common_words) / MapSet.size(total_words)
        similarity > 0.6  # 60% similarity threshold
      else
        false
      end
    rescue
      _ -> false
    end
  end

  # Ensure one-to-one matching (no transaction appears in multiple matches)
  defp ensure_one_to_one_matching(matches) do
    # Sort by confidence score (highest first)
    sorted_matches = Enum.sort_by(matches, & Decimal.to_float(&1.confidence_score), :desc)

    {final_matches, _, _} = Enum.reduce(sorted_matches, {[], MapSet.new(), MapSet.new()},
      fn match, {acc_matches, used_a_ids, used_b_ids} ->
        a_id = match.transaction_a.id
        b_id = match.transaction_b.id

        if MapSet.member?(used_a_ids, a_id) or MapSet.member?(used_b_ids, b_id) do
          # Skip this match as one of the transactions is already matched
          {acc_matches, used_a_ids, used_b_ids}
        else
          # Add this match
          {[match | acc_matches], MapSet.put(used_a_ids, a_id), MapSet.put(used_b_ids, b_id)}
        end
      end)

    Enum.reverse(final_matches)
  end

  # Create match records in database and update transaction statuses
  defp create_matches_and_update_transactions(matches, reconciliation_run_id) do
    Repo.transaction(fn ->
      # First, get ALL transactions for this reconciliation run
      all_transactions = Reconciliation.get_transactions(reconciliation_run_id)

      # Initialize ALL transactions as unmatched first
      Logger.info("Initializing match status for #{length(all_transactions)} transactions")
      Enum.each(all_transactions, fn txn ->
        try do
          Reconciliation.update_transaction_match_status(txn, %{
            is_matched: false,
            match_confidence: nil
          })
        rescue
          error ->
            Logger.error("Error initializing transaction #{txn.id} status: #{inspect(error)}")
        end
      end)

      # Track which transactions get matched
      matched_a_ids = Enum.map(matches, & &1.transaction_a.id) |> MapSet.new()
      matched_b_ids = Enum.map(matches, & &1.transaction_b.id) |> MapSet.new()

      Logger.info("Processing #{length(matches)} matches")

      # Process each match
      Enum.each(matches, fn match ->
        try do
          # Create transaction match record
          match_attrs = %{
            reconciliation_run_id: reconciliation_run_id,
            transaction_a_id: match.transaction_a.id,
            transaction_b_id: match.transaction_b.id,
            match_type: match.match_type,
            confidence_score: match.confidence_score,
            amount_difference: TransactionMatch.calculate_amount_difference(
              match.transaction_a,
              match.transaction_b
            ),
            date_difference_days: TransactionMatch.calculate_date_difference(
              match.transaction_a,
              match.transaction_b
            ),
            matching_criteria: match.matching_criteria
          }

          case Reconciliation.create_transaction_match(match_attrs) do
            {:ok, _transaction_match} ->
              Logger.debug("Successfully created match for A(#{match.transaction_a.id}) <-> B(#{match.transaction_b.id})")

              # Update both transactions as matched
              try do
                Reconciliation.update_transaction_match_status(match.transaction_a, %{
                  is_matched: true,
                  match_confidence: match.confidence_score
                })

                Reconciliation.update_transaction_match_status(match.transaction_b, %{
                  is_matched: true,
                  match_confidence: match.confidence_score
                })
              rescue
                error ->
                  Logger.error("Error updating matched status for A(#{match.transaction_a.id}) <-> B(#{match.transaction_b.id}): #{inspect(error)}")
              end

            {:error, changeset} ->
              Logger.error("Failed to create transaction match for A(#{match.transaction_a.id}) <-> B(#{match.transaction_b.id}): #{inspect(changeset.errors)}")
          end
        rescue
          error ->
            Logger.error("Exception creating match for A(#{match.transaction_a.id}) <-> B(#{match.transaction_b.id}): #{inspect(error)}")
        end
      end)

      # Log final status
      final_matched_count = MapSet.size(matched_a_ids) + MapSet.size(matched_b_ids)
      final_unmatched_count = length(all_transactions) - final_matched_count

      Logger.info("Match processing complete: #{final_matched_count} matched, #{final_unmatched_count} unmatched")

      # Double-check: ensure any remaining transactions are marked as unmatched
      # This is a safety net in case some transactions were missed
      try do
        remaining_transactions = Enum.filter(all_transactions, fn txn ->
          not (MapSet.member?(matched_a_ids, txn.id) or MapSet.member?(matched_b_ids, txn.id))
        end)

        if length(remaining_transactions) > 0 do
          Logger.info("Double-checking #{length(remaining_transactions)} remaining unmatched transactions")
          Enum.each(remaining_transactions, fn txn ->
            Reconciliation.update_transaction_match_status(txn, %{
              is_matched: false,
              match_confidence: nil
            })
          end)
        end
      rescue
        error ->
          Logger.error("Error in final unmatched transaction update: #{inspect(error)}")
      end
    end)
  end

  @doc """
  Manually creates a match between two transactions.
  """
  def create_manual_match(transaction_a_id, transaction_b_id, reconciliation_run_id, notes \\ nil) do
    try do
      # Get transactions
      transaction_a = Repo.get!(Transaction, transaction_a_id)
      transaction_b = Repo.get!(Transaction, transaction_b_id)

      # Verify they belong to different files
      if transaction_a.uploaded_file.file_type == transaction_b.uploaded_file.file_type do
        {:error, "Cannot match transactions from the same file"}
      else
        Repo.transaction(fn ->
          # Create manual match
          match_changeset = TransactionMatch.create_manual_match(
            transaction_a,
            transaction_b,
            reconciliation_run_id,
            notes
          )

          case Repo.insert(match_changeset) do
            {:ok, match} ->
              # Update transaction statuses
              Reconciliation.update_transaction_match_status(transaction_a, %{
                is_matched: true,
                match_confidence: Decimal.new("100")
              })

              Reconciliation.update_transaction_match_status(transaction_b, %{
                is_matched: true,
                match_confidence: Decimal.new("100")
              })

              # Recalculate reconciliation stats
              Reconciliation.calculate_reconciliation_stats(reconciliation_run_id)

              {:ok, match}

            {:error, changeset} ->
              {:error, changeset}
          end
        end)
      end
    rescue
      error ->
        Logger.error("Exception in create_manual_match: #{inspect(error)}")
        {:error, "Failed to create manual match: #{inspect(error)}"}
    end
  end

  @doc """
  Removes a match between two transactions.
  """
  def remove_match(match_id) do
    try do
      match = Repo.get!(TransactionMatch, match_id)
      |> Repo.preload([:transaction_a, :transaction_b])

      Repo.transaction(fn ->
        # Update transaction statuses
        Reconciliation.update_transaction_match_status(match.transaction_a, %{
          is_matched: false,
          match_confidence: nil
        })

        Reconciliation.update_transaction_match_status(match.transaction_b, %{
          is_matched: false,
          match_confidence: nil
        })

        # Delete the match
        Repo.delete!(match)

        # Recalculate reconciliation stats
        Reconciliation.calculate_reconciliation_stats(match.reconciliation_run_id)
      end)
    rescue
      error ->
        Logger.error("Exception in remove_match: #{inspect(error)}")
        {:error, "Failed to remove match: #{inspect(error)}"}
    end
  end
end
