#!/usr/bin/env python3
"""
<PERSON>ript to convert CSV test files to Excel format for reconciliation testing.
Requires: pip install pandas openpyxl
"""

import pandas as pd
import os
import glob
from pathlib import Path

def convert_csv_to_excel(csv_file_path, excel_file_path):
    """Convert a single CSV file to Excel format."""
    try:
        # Read CSV file
        df = pd.read_csv(csv_file_path)
        
        # Write to Excel file
        with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Transactions', index=False)
        
        print(f"Converted: {csv_file_path} -> {excel_file_path}")
        return True
    except Exception as e:
        print(f"Error converting {csv_file_path}: {str(e)}")
        return False

def main():
    """Convert all CSV files in the csv directory to Excel format."""
    # Get the directory containing this script
    script_dir = Path(__file__).parent
    csv_dir = script_dir / "csv"
    excel_dir = script_dir / "excel"
    
    # Ensure excel directory exists
    excel_dir.mkdir(exist_ok=True)
    
    # Find all CSV files
    csv_files = glob.glob(str(csv_dir / "*.csv"))
    
    if not csv_files:
        print("No CSV files found in the csv directory.")
        return
    
    print(f"Found {len(csv_files)} CSV files to convert...")
    
    success_count = 0
    for csv_file in csv_files:
        # Generate Excel filename
        csv_filename = Path(csv_file).name
        excel_filename = csv_filename.replace('.csv', '.xlsx')
        excel_file_path = excel_dir / excel_filename
        
        # Convert the file
        if convert_csv_to_excel(csv_file, excel_file_path):
            success_count += 1
    
    print(f"\nConversion complete: {success_count}/{len(csv_files)} files converted successfully.")

if __name__ == "__main__":
    main()
