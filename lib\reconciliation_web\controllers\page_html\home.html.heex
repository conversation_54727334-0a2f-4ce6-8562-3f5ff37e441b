<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ReconcilePro - Efficient Financial Reconciliation</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Professional Slate & Emerald Color Scheme */
            --primary-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --secondary-gradient: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            --accent-gradient: linear-gradient(135deg, #10b981 0%, #047857 100%);

            /* Background Colors */
            --light-bg: #ffffff;
            --lighter-bg: #f9fafb;
            --dark-bg: #1f2937;
            --darker-bg: #111827;

            /* Glass Effects */
            --glass-bg: rgba(255, 255, 255, 0.95);
            --glass-bg-dark: rgba(31, 41, 55, 0.95);
            --glass-border: rgba(107, 114, 128, 0.2);
            --glass-border-light: rgba(229, 231, 235, 0.8);

            /* Text Colors */
            --text-primary: #111827;
            --text-primary-light: #ffffff;
            --text-secondary: #6b7280;
            --text-secondary-light: #9ca3af;

            /* Accent Colors */
            --emerald-primary: #10b981;
            --emerald-secondary: #059669;
            --emerald-light: #d1fae5;
            --slate-primary: #475569;
            --slate-secondary: #334155;
            --slate-light: #f1f5f9;

            /* Status Colors */
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #3b82f6;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--light-bg) 0%, var(--lighter-bg) 100%);
            color: var(--text-primary);
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* Animated Background */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: radial-gradient(circle at 20% 50%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
                       radial-gradient(circle at 80% 20%, rgba(107, 114, 128, 0.03) 0%, transparent 50%),
                       radial-gradient(circle at 40% 80%, rgba(16, 185, 129, 0.08) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(-10px) rotate(-1deg); }
        }

        /* Glassmorphism utilities */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border-light);
            border-radius: 20px;
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border-light);
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(107, 114, 128, 0.1);
        }

        /* Navigation */
        .nav {
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            padding: 20px 0;
            transition: all 0.3s ease;
        }

        .nav.scrolled {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border-light);
        }

        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--accent-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-links a:hover {
            color: var(--emerald-primary);
            transform: translateY(-2px);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -5px;
            left: 50%;
            background: var(--accent-gradient);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .nav-cta {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--accent-gradient);
            color: white;
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.25);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(16, 185, 129, 0.35);
        }

        .btn-ghost {
            background: transparent;
            color: var(--text-primary);
            border: 2px solid var(--slate-primary);
        }

        .btn-ghost:hover {
            background: var(--emerald-light);
            color: var(--emerald-secondary);
            border-color: var(--emerald-primary);
            transform: translateY(-3px);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            padding: 0 2rem;
            overflow: hidden;
        }

        .hero-container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .hero-content h1 {
            font-size: 4rem;
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--text-primary) 0%, var(--slate-primary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-content .highlight {
            background: var(--accent-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }

        .hero-content p {
            font-size: 1.3rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .hero-cta {
            display: flex;
            gap: 1rem;
            margin-bottom: 3rem;
        }

        .hero-stats {
            display: flex;
            gap: 2rem;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            background: var(--accent-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        /* Hero Visual */
        .hero-visual {
            position: relative;
            height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-card {
            position: absolute;
            padding: 1.5rem;
            border-radius: 16px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border-light);
            animation: float-card 6s ease-in-out infinite;
        }

        .floating-card:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-card:nth-child(2) {
            top: 20%;
            right: 15%;
            animation-delay: 2s;
        }

        .floating-card:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float-card {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(2deg); }
        }

        .dashboard-preview {
            width: 100%;
            max-width: 500px;
            height: 350px;
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border-radius: 20px;
            border: 1px solid var(--glass-border);
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        /* Sections */
        .section {
            padding: 6rem 2rem;
            position: relative;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-subtitle {
            font-size: 1rem;
            color: var(--emerald-primary);
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .section-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--text-primary) 0%, var(--slate-primary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-description {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .feature-card {
            padding: 2.5rem;
            border-radius: 24px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border-light);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--accent-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            background: var(--light-bg);
            border-color: var(--emerald-primary);
            box-shadow: 0 25px 50px rgba(16, 185, 129, 0.15);
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            background: var(--accent-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.25);
        }

        .feature-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* How it works */
        .steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-top: 4rem;
        }

        .step {
            text-align: center;
            position: relative;
        }

        .step-number {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--accent-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 auto 2rem;
            box-shadow: 0 15px 35px rgba(16, 185, 129, 0.3);
        }

        .step-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .step-description {
            color: var(--text-secondary);
        }

        /* Testimonials */
        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .testimonial {
            padding: 2.5rem;
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border-radius: 24px;
            border: 1px solid var(--glass-border);
            position: relative;
        }

        .testimonial-quote {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            color: var(--text-primary);
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--accent-gradient);
        }

        .author-info h4 {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .author-info p {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        /* CTA Section */
        .cta-section {
            background: linear-gradient(135deg, var(--emerald-light) 0%, var(--slate-light) 100%);
            border-radius: 40px;
            padding: 4rem;
            text-align: center;
            margin: 4rem 0;
            border: 1px solid var(--glass-border-light);
        }

        .cta-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .cta-description {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        /* Footer */
        .footer {
            padding: 3rem 2rem 2rem;
            border-top: 1px solid var(--glass-border-light);
            background: var(--slate-light);
        }

        .footer-content {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .footer-section a {
            color: var(--text-secondary);
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
            transition: color 0.3s ease;
        }

        .footer-section a:hover {
            color: var(--emerald-primary);
        }

        .footer-bottom {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-container {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 2rem;
            }

            .hero-content h1 {
                font-size: 2.5rem;
                line-height: 1.2;
            }

            .hero-content p {
                font-size: 1.1rem;
            }

            .hero-cta {
                flex-direction: column;
                gap: 0.75rem;
            }

            .hero-cta .btn {
                width: 100%;
                text-align: center;
            }

            .hero-stats {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                display: none;
            }

            .nav-cta {
                gap: 0.5rem;
            }

            .nav-cta .btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }

            .mobile-menu-btn {
                display: block !important;
            }

            .mobile-menu a:hover {
                background: var(--emerald-light);
                color: var(--emerald-secondary);
            }

            .section {
                padding: 4rem 1rem;
            }

            .section-title {
                font-size: 2rem;
                line-height: 1.2;
            }

            .section-description {
                font-size: 1rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .feature-card {
                padding: 2rem;
            }

            .steps {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .testimonials-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .testimonial {
                padding: 2rem;
            }

            /* Demo section mobile */
            .demo-steps {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .demo-step {
                padding: 1.5rem;
            }

            .demo-viewer {
                padding: 1.5rem;
                min-height: 300px;
            }

            .demo-content h3 {
                font-size: 1.2rem;
            }

            /* Pricing mobile */
            .cta-section {
                padding: 2rem;
                margin: 2rem 0;
            }

            .cta-title {
                font-size: 2rem;
            }

            .cta-description {
                font-size: 1rem;
            }

            /* Modal mobile */
            .modal-content {
                margin: 10% auto;
                width: 95%;
                padding: 1.5rem;
            }

            .modal-content h2 {
                font-size: 1.5rem;
            }

            .modal-content form > div:first-child {
                grid-template-columns: 1fr;
            }

            /* Footer mobile */
            .footer-content {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                text-align: center;
            }
        }

        @media (max-width: 480px) {
            .hero-content h1 {
                font-size: 2rem;
            }

            .section-title {
                font-size: 1.75rem;
            }

            .feature-card,
            .testimonial {
                padding: 1.5rem;
            }

            .demo-viewer {
                padding: 1rem;
            }

            .btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }
        }

        /* Touch improvements */
        @media (hover: none) and (pointer: coarse) {
            .btn:hover {
                transform: none;
            }

            .feature-card:hover,
            .testimonial:hover {
                transform: none;
            }

            .nav-links a:hover {
                transform: none;
            }

            /* Larger touch targets */
            .btn {
                min-height: 44px;
                padding: 12px 24px;
            }

            .demo-step {
                min-height: 120px;
                cursor: pointer;
                -webkit-tap-highlight-color: rgba(79, 172, 254, 0.2);
            }

            .close {
                min-width: 44px;
                min-height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeInUp 0.8s ease forwards;
        }

        /* Particle system */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--emerald-primary);
            border-radius: 50%;
            opacity: 0.4;
            animation: particle-float 15s linear infinite;
        }

        @keyframes particle-float {
            0% {
                transform: translateY(100vh) translateX(0px);
                opacity: 0;
            }
            10% {
                opacity: 0.6;
            }
            90% {
                opacity: 0.6;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }

        /* Demo Section Styles */
        .demo-step:hover {
            transform: scale(1.05) !important;
            border: 2px solid var(--emerald-primary) !important;
        }

        .demo-content {
            animation: fadeInDemo 0.5s ease forwards;
        }

        @keyframes fadeInDemo {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .upload-progress div {
            animation: progressFill 2s ease-in-out;
        }

        @keyframes progressFill {
            from { width: 0%; }
            to { width: 100%; }
        }

        .matching-item {
            animation: slideInMatch 0.6s ease forwards;
        }

        .matching-item:nth-child(1) { animation-delay: 0.2s; }
        .matching-item:nth-child(2) { animation-delay: 0.4s; }
        .matching-item:nth-child(3) { animation-delay: 0.6s; }

        @keyframes slideInMatch {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Modal Styles */
        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal input::placeholder,
        .modal textarea::placeholder,
        .modal select option {
            color: var(--text-secondary);
        }

        .modal input:focus,
        .modal textarea:focus,
        .modal select:focus {
            outline: none;
            border-color: var(--emerald-primary);
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
        }

        .close:hover {
            color: var(--emerald-primary) !important;
        }

        /* Feature Showcase Styles */
        .showcase-tab:hover {
            background: var(--emerald-light) !important;
            color: var(--emerald-secondary) !important;
        }

        .showcase-tab.active {
            background: var(--emerald-primary) !important;
            color: white !important;
        }

        .feature-view {
            animation: fadeInFeature 0.5s ease forwards;
        }

        @keyframes fadeInFeature {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body class="bg-blue-900">
    <!-- Navigation -->
    <nav class="nav" id="navbar">
        <div class="nav-container">
            <div class="logo">ReconcilePro</div>
            <ul class="nav-links">
                <li><a href="#features">Features</a></li>
                <li><a href="#how-it-works">How it Works</a></li>
                <li><a href="#testimonials">Testimonials</a></li>
                <li><a href="#pricing">Pricing</a></li>
            </ul>
            <div class="nav-cta">
                <a href="/users/log_in" class="btn btn-ghost">Sign In</a>
                <button class="mobile-menu-btn" onclick="toggleMobileMenu()" style="display: none; background: none; border: none; color: var(--text-primary); font-size: 1.5rem; cursor: pointer; padding: 0.5rem;">☰</button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div class="mobile-menu" id="mobileMenu" style="display: none; position: absolute; top: 100%; left: 0; right: 0; background: var(--glass-bg); backdrop-filter: blur(20px); border-top: 1px solid var(--glass-border-light); padding: 1rem;">
            <ul style="list-style: none; display: flex; flex-direction: column; gap: 1rem;">
                <li><a href="#features" onclick="closeMobileMenu()" style="color: var(--text-secondary); text-decoration: none; padding: 0.75rem; display: block; border-radius: 8px; transition: all 0.3s ease;">Features</a></li>
                <li><a href="#how-it-works" onclick="closeMobileMenu()" style="color: var(--text-secondary); text-decoration: none; padding: 0.75rem; display: block; border-radius: 8px; transition: all 0.3s ease;">How it Works</a></li>
                <li><a href="#testimonials" onclick="closeMobileMenu()" style="color: var(--text-secondary); text-decoration: none; padding: 0.75rem; display: block; border-radius: 8px; transition: all 0.3s ease;">Testimonials</a></li>
                <li><a href="#pricing" onclick="closeMobileMenu()" style="color: var(--text-secondary); text-decoration: none; padding: 0.75rem; display: block; border-radius: 8px; transition: all 0.3s ease;">Pricing</a></li>
                <li><a href="/users/register" onclick="closeMobileMenu()" class="btn btn-primary" style="margin-top: 0.5rem;">Get Started</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="particles"></div>
        <div class="hero-container">
            <div class="hero-content fade-in">
                <h1>
                    Efficient <br>
                    <span class="highlight">Financial Reconciliation</span><br>
                    Made Simple
                </h1>
                <p>
                    Transform your financial operations with powerful automation. 
                    Reconcile millions of transactions in seconds, detect anomalies instantly, 
                    and maintain perfect accuracy across all your financial data.
                </p>
                <div class="hero-cta">
                    <a href="/users/register" class="btn btn-primary">Get Started</a>
                    <a href="#demo" class="btn btn-ghost">Watch Demo</a>
                </div>
                <div class="hero-stats">
                    <div class="stat">
                        <div class="stat-number" data-target="99.9" data-suffix="%">0%</div>
                        <div class="stat-label">Accuracy Rate</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" data-target="10" data-suffix="M+">0M+</div>
                        <div class="stat-label">Transactions/Day</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" data-target="95" data-suffix="%">0%</div>
                        <div class="stat-label">Time Savings</div>
                    </div>
                </div>
            </div>
            <div class="hero-visual">
                <!-- Feature Showcase Tabs -->
                <div class="feature-showcase" style="width: 100%; max-width: 500px;">
                    <div class="showcase-tabs" style="display: flex; background: var(--glass-bg); border-radius: 12px 12px 0 0; border: 1px solid var(--glass-border-light); border-bottom: none;">
                        <button class="showcase-tab active" onclick="showFeature('dashboard')" style="flex: 1; padding: 0.75rem; background: var(--emerald-primary); color: white; border: none; border-radius: 12px 0 0 0; font-size: 0.8rem; font-weight: 600; cursor: pointer; transition: all 0.3s ease;">Dashboard</button>
                        <button class="showcase-tab" onclick="showFeature('upload')" style="flex: 1; padding: 0.75rem; background: transparent; color: var(--text-secondary); border: none; font-size: 0.8rem; font-weight: 600; cursor: pointer; transition: all 0.3s ease;">Upload</button>
                        <button class="showcase-tab" onclick="showFeature('results')" style="flex: 1; padding: 0.75rem; background: transparent; color: var(--text-secondary); border: none; border-radius: 0 12px 0 0; font-size: 0.8rem; font-weight: 600; cursor: pointer; transition: all 0.3s ease;">Results</button>
                    </div>

                    <div class="dashboard-preview glass-card" style="border-radius: 0 0 12px 12px; border-top: none;">
                        <!-- Dashboard View -->
                        <div id="feature-dashboard" class="feature-view" style="width: 100%; height: 100%; padding: 1rem; overflow: hidden; display: block;">
                            <!-- Dashboard Header -->
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                <h3 style="font-size: 1rem; font-weight: 700; color: var(--text-primary); margin: 0;">Financial Overview</h3>
                                <button style="background: var(--emerald-primary); color: white; padding: 0.25rem 0.75rem; border-radius: 6px; font-size: 0.75rem; border: none;">+ New</button>
                            </div>

                            <!-- Summary Cards -->
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 0.75rem; margin-bottom: 1rem;">
                                <div style="background: var(--light-bg); border: 1px solid var(--glass-border-light); border-radius: 8px; padding: 0.75rem; text-align: center;">
                                    <div style="font-size: 0.7rem; color: var(--text-secondary); margin-bottom: 0.25rem;">Total Transactions</div>
                                    <div style="font-size: 1.25rem; font-weight: 700; color: var(--emerald-primary);">24,847</div>
                                </div>
                                <div style="background: var(--light-bg); border: 1px solid var(--glass-border-light); border-radius: 8px; padding: 0.75rem; text-align: center;">
                                    <div style="font-size: 0.7rem; color: var(--text-secondary); margin-bottom: 0.25rem;">Match Rate</div>
                                    <div style="font-size: 1.25rem; font-weight: 700; color: var(--success);">98.7%</div>
                                </div>
                                <div style="background: var(--light-bg); border: 1px solid var(--glass-border-light); border-radius: 8px; padding: 0.75rem; text-align: center;">
                                    <div style="font-size: 0.7rem; color: var(--text-secondary); margin-bottom: 0.25rem;">Discrepancies</div>
                                    <div style="font-size: 1.25rem; font-weight: 700; color: var(--error);">23</div>
                                </div>
                            </div>

                            <!-- Recent Runs Table -->
                            <div style="background: var(--light-bg); border: 1px solid var(--glass-border-light); border-radius: 8px; padding: 0.75rem;">
                                <div style="font-size: 0.8rem; font-weight: 600; color: var(--text-primary); margin-bottom: 0.5rem;">Recent Reconciliations</div>
                                <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: var(--lighter-bg); border-radius: 4px;">
                                        <div>
                                            <div style="font-size: 0.7rem; font-weight: 600; color: var(--text-primary);">Bank Reconciliation Q4</div>
                                            <div style="font-size: 0.6rem; color: var(--text-secondary);">Dec 15, 2024</div>
                                        </div>
                                        <span style="background: var(--emerald-light); color: var(--emerald-secondary); padding: 0.125rem 0.5rem; border-radius: 12px; font-size: 0.6rem; font-weight: 600;">Completed</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: var(--lighter-bg); border-radius: 4px;">
                                        <div>
                                            <div style="font-size: 0.7rem; font-weight: 600; color: var(--text-primary);">ERP vs Bank Nov</div>
                                            <div style="font-size: 0.6rem; color: var(--text-secondary);">Nov 30, 2024</div>
                                        </div>
                                        <span style="background: var(--emerald-light); color: var(--emerald-secondary); padding: 0.125rem 0.5rem; border-radius: 12px; font-size: 0.6rem; font-weight: 600;">Completed</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: var(--lighter-bg); border-radius: 4px;">
                                        <div>
                                            <div style="font-size: 0.7rem; font-weight: 600; color: var(--text-primary);">Monthly Reconciliation</div>
                                            <div style="font-size: 0.6rem; color: var(--text-secondary);">Nov 15, 2024</div>
                                        </div>
                                        <span style="background: rgba(245, 158, 11, 0.1); color: var(--warning); padding: 0.125rem 0.5rem; border-radius: 12px; font-size: 0.6rem; font-weight: 600;">Processing</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Upload View -->
                        <div id="feature-upload" class="feature-view" style="width: 100%; height: 100%; padding: 1rem; overflow: hidden; display: none;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                <h3 style="font-size: 1rem; font-weight: 700; color: var(--text-primary); margin: 0;">Upload Files</h3>
                            </div>

                            <!-- Upload Areas -->
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.75rem; margin-bottom: 1rem;">
                                <div style="border: 2px dashed var(--emerald-primary); border-radius: 8px; padding: 1rem; text-align: center; background: var(--emerald-light);">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">📄</div>
                                    <div style="font-size: 0.8rem; font-weight: 600; color: var(--text-primary); margin-bottom: 0.25rem;">Bank Statement</div>
                                    <div style="font-size: 0.7rem; color: var(--text-secondary);">CSV, Excel</div>
                                </div>
                                <div style="border: 2px dashed var(--slate-primary); border-radius: 8px; padding: 1rem; text-align: center; background: var(--slate-light);">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">📊</div>
                                    <div style="font-size: 0.8rem; font-weight: 600; color: var(--text-primary); margin-bottom: 0.25rem;">ERP Export</div>
                                    <div style="font-size: 0.7rem; color: var(--text-secondary);">CSV, Excel</div>
                                </div>
                            </div>

                            <!-- Process Steps -->
                            <div style="background: var(--light-bg); border: 1px solid var(--glass-border-light); border-radius: 8px; padding: 0.75rem;">
                                <div style="font-size: 0.8rem; font-weight: 600; color: var(--text-primary); margin-bottom: 0.5rem;">Processing Steps</div>
                                <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                                        <div style="width: 1rem; height: 1rem; border-radius: 50%; background: var(--emerald-primary); display: flex; align-items: center; justify-content: center; color: white; font-size: 0.6rem; font-weight: 600;">✓</div>
                                        <span style="font-size: 0.7rem; color: var(--text-primary);">Files uploaded successfully</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                                        <div style="width: 1rem; height: 1rem; border-radius: 50%; background: var(--emerald-primary); display: flex; align-items: center; justify-content: center; color: white; font-size: 0.6rem; font-weight: 600;">✓</div>
                                        <span style="font-size: 0.7rem; color: var(--text-primary);">Data validation complete</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                                        <div style="width: 1rem; height: 1rem; border-radius: 50%; background: var(--warning); display: flex; align-items: center; justify-content: center; color: white; font-size: 0.6rem; font-weight: 600;">⟳</div>
                                        <span style="font-size: 0.7rem; color: var(--text-primary);">Matching transactions...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Results View -->
                        <div id="feature-results" class="feature-view" style="width: 100%; height: 100%; padding: 1rem; overflow: hidden; display: none;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                <h3 style="font-size: 1rem; font-weight: 700; color: var(--text-primary); margin: 0;">Reconciliation Results</h3>
                                <button style="background: var(--emerald-primary); color: white; padding: 0.25rem 0.75rem; border-radius: 6px; font-size: 0.75rem; border: none;">Export</button>
                            </div>

                            <!-- Results Summary -->
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 0.75rem; margin-bottom: 1rem;">
                                <div style="background: var(--emerald-light); border: 1px solid var(--emerald-primary); border-radius: 8px; padding: 0.75rem; text-align: center;">
                                    <div style="font-size: 1.25rem; font-weight: 700; color: var(--emerald-secondary); margin-bottom: 0.25rem;">1,247</div>
                                    <div style="font-size: 0.7rem; color: var(--text-secondary);">Matched</div>
                                </div>
                                <div style="background: rgba(239, 68, 68, 0.1); border: 1px solid var(--error); border-radius: 8px; padding: 0.75rem; text-align: center;">
                                    <div style="font-size: 1.25rem; font-weight: 700; color: var(--error); margin-bottom: 0.25rem;">3</div>
                                    <div style="font-size: 0.7rem; color: var(--text-secondary);">Discrepancies</div>
                                </div>
                                <div style="background: rgba(59, 130, 246, 0.1); border: 1px solid var(--info); border-radius: 8px; padding: 0.75rem; text-align: center;">
                                    <div style="font-size: 1.25rem; font-weight: 700; color: var(--info); margin-bottom: 0.25rem;">99.8%</div>
                                    <div style="font-size: 0.7rem; color: var(--text-secondary);">Accuracy</div>
                                </div>
                            </div>

                            <!-- Transaction Matches -->
                            <div style="background: var(--light-bg); border: 1px solid var(--glass-border-light); border-radius: 8px; padding: 0.75rem;">
                                <div style="font-size: 0.8rem; font-weight: 600; color: var(--text-primary); margin-bottom: 0.5rem;">Transaction Matches</div>
                                <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: var(--emerald-light); border-radius: 4px; border-left: 3px solid var(--emerald-primary);">
                                        <div>
                                            <div style="font-size: 0.7rem; font-weight: 600; color: var(--text-primary);">MK 2,847.92</div>
                                            <div style="font-size: 0.6rem; color: var(--text-secondary);">REF-2024-001 ↔ TXN-001</div>
                                        </div>
                                        <span style="color: var(--emerald-secondary); font-size: 0.7rem; font-weight: 600;">100%</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: rgba(239, 68, 68, 0.1); border-radius: 4px; border-left: 3px solid var(--error);">
                                        <div>
                                            <div style="font-size: 0.7rem; font-weight: 600; color: var(--text-primary);">MK 145.00</div>
                                            <div style="font-size: 0.6rem; color: var(--text-secondary);">No match found</div>
                                        </div>
                                        <span style="color: var(--error); font-size: 0.7rem; font-weight: 600;">0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Floating Cards -->
                <div class="floating-card">
                    <div style="color: var(--success); font-weight: 600;">✓ Transaction Matched</div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">MK2,847.92 • Bank Transfer</div>
                </div>
                <div class="floating-card">
                    <div style="color: var(--warning); font-weight: 600;">⚠ Discrepancy Found</div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">MK145.00 • Investigate</div>
                </div>
                <div class="floating-card">
                    <div style="color: var(--emerald-primary); font-weight: 600;">📊 Report Generated</div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Monthly Reconciliation</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Demo Section -->
    <section class="section" id="demo">
        <div class="container">
            <div class="section-header">
                <div class="section-subtitle">Interactive Demo</div>
                <h2 class="section-title">See ReconcilePro in Action</h2>
                <p class="section-description">
                    Experience the power of automated reconciliation with our interactive demo.
                    Upload sample files and watch the magic happen in real-time.
                </p>
            </div>

            <div class="demo-container" style="max-width: 1000px; margin: 0 auto;">
                <div class="demo-steps" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-bottom: 3rem;">
                    <div class="demo-step glass-card" style="padding: 2rem; text-align: center; cursor: pointer; transition: all 0.3s ease;" onclick="showDemoStep(1)">
                        <div class="demo-step-icon" style="width: 60px; height: 60px; border-radius: 50%; background: var(--accent-gradient); margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">📁</div>
                        <h3 style="margin-bottom: 1rem; font-weight: 700;">Upload Files</h3>
                        <p style="color: var(--text-secondary);">Upload your transaction files from different sources</p>
                    </div>

                    <div class="demo-step glass-card" style="padding: 2rem; text-align: center; cursor: pointer; transition: all 0.3s ease;" onclick="showDemoStep(2)">
                        <div class="demo-step-icon" style="width: 60px; height: 60px; border-radius: 50%; background: var(--accent-gradient); margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">⚡</div>
                        <h3 style="margin-bottom: 1rem; font-weight: 700;">Auto-Match</h3>
                        <p style="color: var(--text-secondary);">AI-powered matching finds corresponding transactions</p>
                    </div>

                    <div class="demo-step glass-card" style="padding: 2rem; text-align: center; cursor: pointer; transition: all 0.3s ease;" onclick="showDemoStep(3)">
                        <div class="demo-step-icon" style="width: 60px; height: 60px; border-radius: 50%; background: var(--accent-gradient); margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">📊</div>
                        <h3 style="margin-bottom: 1rem; font-weight: 700;">View Results</h3>
                        <p style="color: var(--text-secondary);">Get detailed reports and identify discrepancies</p>
                    </div>
                </div>

                <div class="demo-viewer glass-card" style="padding: 2rem; min-height: 400px; position: relative; overflow: hidden;">
                    <div id="demo-step-1" class="demo-content active" style="display: block;">
                        <h3 style="margin-bottom: 2rem; text-align: center; font-size: 1.5rem;">Step 1: Upload Your Files</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; align-items: center;">
                            <div class="file-upload-demo" style="border: 2px dashed var(--emerald-primary); border-radius: 12px; padding: 2rem; text-align: center; background: var(--emerald-light);">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">📄</div>
                                <h4 style="margin-bottom: 0.5rem; color: var(--text-primary);">Bank Statement A</h4>
                                <p style="color: var(--text-secondary); font-size: 0.9rem;">1,247 transactions</p>
                                <div class="upload-progress" style="width: 100%; height: 4px; background: var(--slate-light); border-radius: 2px; margin-top: 1rem; overflow: hidden;">
                                    <div style="width: 100%; height: 100%; background: var(--emerald-primary); border-radius: 2px;"></div>
                                </div>
                            </div>
                            <div class="file-upload-demo" style="border: 2px dashed var(--slate-primary); border-radius: 12px; padding: 2rem; text-align: center; background: var(--slate-light);">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">📄</div>
                                <h4 style="margin-bottom: 0.5rem; color: var(--text-primary);">ERP Export B</h4>
                                <p style="color: var(--text-secondary); font-size: 0.9rem;">1,198 transactions</p>
                                <div class="upload-progress" style="width: 100%; height: 4px; background: var(--emerald-light); border-radius: 2px; margin-top: 1rem; overflow: hidden;">
                                    <div style="width: 100%; height: 100%; background: var(--slate-primary); border-radius: 2px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="demo-step-2" class="demo-content" style="display: none;">
                        <h3 style="margin-bottom: 2rem; text-align: center; font-size: 1.5rem;">Step 2: Intelligent Matching</h3>
                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <div class="matching-item" style="display: flex; align-items: center; padding: 1rem; background: rgba(16, 185, 129, 0.1); border-radius: 8px; border-left: 4px solid #10b981;">
                                <span style="color: #10b981; font-size: 1.2rem; margin-right: 1rem;">✓</span>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600;">MK 2,847.92 - Bank Transfer</div>
                                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Matched: REF-2024-001 ↔ TXN-001-2024</div>
                                </div>
                                <div style="color: #10b981; font-weight: 600;">100% Match</div>
                            </div>
                            <div class="matching-item" style="display: flex; align-items: center; padding: 1rem; background: rgba(16, 185, 129, 0.1); border-radius: 8px; border-left: 4px solid #10b981;">
                                <span style="color: #10b981; font-size: 1.2rem; margin-right: 1rem;">✓</span>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600;">MK 1,234.56 - Payment</div>
                                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Matched: REF-2024-002 ↔ TXN-002-2024</div>
                                </div>
                                <div style="color: #10b981; font-weight: 600;">98% Match</div>
                            </div>
                            <div class="matching-item" style="display: flex; align-items: center; padding: 1rem; background: rgba(245, 87, 108, 0.1); border-radius: 8px; border-left: 4px solid #f5576c;">
                                <span style="color: #f5576c; font-size: 1.2rem; margin-right: 1rem;">⚠</span>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600;">MK 145.00 - Discrepancy</div>
                                    <div style="color: var(--text-secondary); font-size: 0.9rem;">No match found - requires investigation</div>
                                </div>
                                <div style="color: #f5576c; font-weight: 600;">0% Match</div>
                            </div>
                        </div>
                        <div style="text-align: center; margin-top: 2rem; padding: 1rem; background: var(--emerald-light); border-radius: 8px; border: 1px solid var(--emerald-primary);">
                            <div style="font-size: 1.1rem; font-weight: 600; margin-bottom: 0.5rem; color: var(--emerald-secondary);">Processing Complete</div>
                            <div style="color: var(--text-secondary);">1,195 matches found • 3 discrepancies • 99.7% accuracy</div>
                        </div>
                    </div>

                    <div id="demo-step-3" class="demo-content" style="display: none;">
                        <h3 style="margin-bottom: 2rem; text-align: center; font-size: 1.5rem;">Step 3: Comprehensive Results</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
                            <div style="text-align: center; padding: 1.5rem; background: var(--emerald-light); border-radius: 12px; border: 1px solid var(--emerald-primary);">
                                <div style="font-size: 2rem; font-weight: 800; color: var(--emerald-secondary); margin-bottom: 0.5rem;">1,195</div>
                                <div style="color: var(--text-secondary);">Matched</div>
                            </div>
                            <div style="text-align: center; padding: 1.5rem; background: rgba(239, 68, 68, 0.1); border-radius: 12px; border: 1px solid var(--error);">
                                <div style="font-size: 2rem; font-weight: 800; color: var(--error); margin-bottom: 0.5rem;">3</div>
                                <div style="color: var(--text-secondary);">Discrepancies</div>
                            </div>
                            <div style="text-align: center; padding: 1.5rem; background: rgba(59, 130, 246, 0.1); border-radius: 12px; border: 1px solid var(--info);">
                                <div style="font-size: 2rem; font-weight: 800; color: var(--info); margin-bottom: 0.5rem;">99.7%</div>
                                <div style="color: var(--text-secondary);">Accuracy</div>
                            </div>
                        </div>
                        <div style="text-align: center;">
                            <a href="/users/register" class="btn btn-primary" style="margin-right: 1rem;">Try It Yourself</a>
                            <button class="btn btn-ghost" onclick="resetDemo()">Reset Demo</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="section" id="features">
        <div class="container">
            <div class="section-header">
                <div class="section-subtitle">Powerful Features</div>
                <h2 class="section-title">Everything You Need for Perfect Reconciliation</h2>
                <p class="section-description">
                    Our platform combines cutting-edge technology with intuitive design 
                    to deliver unmatched accuracy and efficiency in financial reconciliation.
                </p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                            <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Lightning-Fast Matching</h3>
                    <p class="feature-description">
                        Our advanced engine processes millions of transactions in seconds, 
                        automatically matching entries with high accuracy using sophisticated algorithms.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Intelligent Anomaly Detection</h3>
                    <p class="feature-description">
                        Proactively identify discrepancies, unusual patterns, and potential fraud 
                        with our smart detection system that learns from your data.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                            <path d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Real-Time Dashboards</h3>
                    <p class="feature-description">
                        Get instant visibility into your reconciliation status with beautiful, 
                        interactive dashboards and real-time alerts.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                            <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">95% Time Reduction</h3>
                    <p class="feature-description">
                        Eliminate manual processes and reduce reconciliation time from days to minutes 
                        with our automated workflow engine.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                            <path d="M12 15v5l3-3m-3 3l-3-3m3 3V9a3 3 0 013-3h.01M9 5H7a2 2 0 00-2 2v11a2 2 0 002 2h5.5"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Enterprise Integration</h3>
                    <p class="feature-description">
                        Connect seamlessly with your existing ERP, banking, and accounting systems 
                        through our robust API and pre-built connectors.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                            <path d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Bank-Grade Security</h3>
                    <p class="feature-description">
                        Your data is protected with enterprise-grade encryption, SOC 2 compliance, 
                        and multi-factor authentication.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="section" id="how-it-works">
        <div class="container">
            <div class="section-header">
                <div class="section-subtitle">How It Works</div>
                <h2 class="section-title">Three Simple Steps to Financial Clarity</h2>
                <p class="section-description">
                    Get started in minutes with our intuitive platform designed for finance professionals.
                </p>
            </div>
            <div class="steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h3 class="step-title">Connect Your Data Sources</h3>
                    <p class="step-description">
                        Securely connect your banks, ERPs, and financial systems. 
                        Our platform supports 500+ integrations with automatic data sync.
                    </p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h3 class="step-title">Automated Processing</h3>
                    <p class="step-description">
                        Our advanced system analyzes your data, identifies patterns, and automatically 
                        matches transactions with industry-leading accuracy.
                    </p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h3 class="step-title">Review & Export</h3>
                    <p class="step-description">
                        Review matches, investigate exceptions, and generate comprehensive reports. 
                        Export results to your preferred format with full audit trails.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="section" id="testimonials">
        <div class="container">
            <div class="section-header">
                <div class="section-subtitle">Customer Success</div>
                <h2 class="section-title">Trusted by Finance Leaders Worldwide</h2>
                <p class="section-description">
                    See how companies are transforming their financial operations with ReconcilePro.
                </p>
            </div>
            <div class="testimonials-grid">
                <div class="testimonial">
                    <p class="testimonial-quote">
                        "ReconcilePro reduced our month-end reconciliation from 5 days to 2 hours. 
                        The accuracy is incredible - we've eliminated manual errors completely."
                    </p>
                    <div class="testimonial-author">
                        <div class="author-avatar"></div>
                        <div class="author-info">
                            <h4>Sarah Chen</h4>
                            <p>CFO, TechFlow Solutions</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial">
                    <p class="testimonial-quote">
                        "The real-time anomaly detection has saved us millions in fraud prevention. 
                        Best investment we've made in our finance tech stack."
                    </p>
                    <div class="testimonial-author">
                        <div class="author-avatar"></div>
                        <div class="author-info">
                            <h4>Marcus Rodriguez</h4>
                            <p>Finance Director, Global Bank Corp</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial">
                    <p class="testimonial-quote">
                        "Implementation was seamless. Within weeks, we were processing 10x more 
                        transactions with half the team. Game-changing technology."
                    </p>
                    <div class="testimonial-author">
                        <div class="author-avatar"></div>
                        <div class="author-info">
                            <h4>Lisa Thompson</h4>
                            <p>Head of Accounting, MegaCorp Inc</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial">
                    <p class="testimonial-quote">
                        "The reporting capabilities are phenomenal. Board presentations have 
                        never been easier with their automated compliance reports."
                    </p>
                    <div class="testimonial-author">
                        <div class="author-avatar"></div>
                        <div class="author-info">
                            <h4>David Park</h4>
                            <p>Controller, Innovation Labs</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="section" id="pricing">
        <div class="container">
            <div class="section-header">
                <div class="section-subtitle">Pricing</div>
                <h2 class="section-title">Choose Your Perfect Plan</h2>
                <p class="section-description">
                    Transparent pricing that scales with your business. No hidden fees, no surprises.
                </p>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 2rem; margin-top: 4rem;">
                <div class="glass-card" style="padding: 2.5rem; text-align: center; position: relative;">
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem;">Starter</h3>
                    <div style="font-size: 3rem; font-weight: 800; background: var(--accent-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 0.5rem;">MK299</div>
                    <p style="color: var(--text-secondary); margin-bottom: 2rem;">per month</p>
                    <ul style="list-style: none; text-align: left; margin-bottom: 2rem;">
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Up to 100K transactions/month
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Basic matching
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Standard reporting
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Email support
                        </li>
                    </ul>
                    <a href="/users/register" class="btn btn-primary" style="width: 100%;">Get Started</a>
                </div>
                
                <div class="glass-card" style="padding: 2.5rem; text-align: center; position: relative; border: 2px solid var(--neon-blue); transform: scale(1.05);">
                    <div style="position: absolute; top: -12px; left: 50%; transform: translateX(-50%); background: var(--accent-gradient); padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.8rem; font-weight: 600;">MOST POPULAR</div>
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem;">Professional</h3>
                    <div style="font-size: 3rem; font-weight: 800; background: var(--accent-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 0.5rem;">MK799</div>
                    <p style="color: var(--text-secondary); margin-bottom: 2rem;">per month</p>
                    <ul style="list-style: none; text-align: left; margin-bottom: 2rem;">
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Up to 1M transactions/month
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Advanced matching + anomaly detection
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Real-time dashboards
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> API integrations
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Priority support
                        </li>
                    </ul>
                    <a href="/users/register" class="btn btn-primary" style="width: 100%;">Get Started</a>
                </div>
                
                <div class="glass-card" style="padding: 2.5rem; text-align: center; position: relative;">
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem;">Enterprise</h3>
                    <div style="font-size: 3rem; font-weight: 800; background: var(--accent-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 0.5rem;">Custom</div>
                    <p style="color: var(--text-secondary); margin-bottom: 2rem;">pricing</p>
                    <ul style="list-style: none; text-align: left; margin-bottom: 2rem;">
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Unlimited transactions
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Custom matching solutions
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> White-label solution
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> Dedicated account manager
                        </li>
                        <li style="padding: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                            <span style="color: var(--neon-blue);">✓</span> 24/7 support
                        </li>
                    </ul>
                    <a href="/users/register" class="btn btn-primary" style="width: 100%;">Get Started</a>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section">
        <div class="container">
            <div class="cta-section">
                <h2 class="cta-title">Ready to Transform Your Financial Operations?</h2>
                <p class="cta-description">
                    Join thousands of finance professionals who trust ReconcilePro for accurate, 
                    fast, and reliable financial reconciliation.
                </p>
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <a href="/users/register" class="btn btn-primary">Start Free Trial</a>
                    <a href="/users/log_in" class="btn btn-ghost">Sign In</a>
                </div>
                <p style="margin-top: 1rem; color: var(--text-secondary); font-size: 0.9rem;">
                    ✓ No credit card required • ✓ Setup in 5 minutes
                </p>
            </div>
        </div>
    </section>

    <!-- Contact Modal -->
    <div id="contactModal" class="modal" style="display: none; position: fixed; z-index: 2000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.8); backdrop-filter: blur(5px);">
        <div class="modal-content glass-card" style="position: relative; margin: 5% auto; padding: 2rem; width: 90%; max-width: 500px; animation: modalSlideIn 0.3s ease;">
            <span class="close" onclick="closeContactModal()" style="position: absolute; top: 1rem; right: 1.5rem; font-size: 2rem; cursor: pointer; color: var(--text-secondary); transition: color 0.3s ease;">&times;</span>

            <h2 style="margin-bottom: 1.5rem; font-size: 1.8rem; font-weight: 700; text-align: center;">Contact Our Sales Team</h2>
            <p style="color: var(--text-secondary); text-align: center; margin-bottom: 2rem;">Get a personalized demo and custom pricing for your enterprise needs.</p>

            <form id="contactForm" style="display: flex; flex-direction: column; gap: 1rem;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <input type="text" name="firstName" placeholder="First Name" required style="padding: 0.75rem; border: 1px solid var(--glass-border); border-radius: 8px; background: rgba(255,255,255,0.1); color: var(--text-primary); backdrop-filter: blur(10px);">
                    <input type="text" name="lastName" placeholder="Last Name" required style="padding: 0.75rem; border: 1px solid var(--glass-border); border-radius: 8px; background: rgba(255,255,255,0.1); color: var(--text-primary); backdrop-filter: blur(10px);">
                </div>
                <input type="email" name="email" placeholder="Work Email" required style="padding: 0.75rem; border: 1px solid var(--glass-border); border-radius: 8px; background: rgba(255,255,255,0.1); color: var(--text-primary); backdrop-filter: blur(10px);">
                <input type="text" name="company" placeholder="Company Name" required style="padding: 0.75rem; border: 1px solid var(--glass-border); border-radius: 8px; background: rgba(255,255,255,0.1); color: var(--text-primary); backdrop-filter: blur(10px);">
                <input type="tel" name="phone" placeholder="Phone Number" style="padding: 0.75rem; border: 1px solid var(--glass-border); border-radius: 8px; background: rgba(255,255,255,0.1); color: var(--text-primary); backdrop-filter: blur(10px);">
                <select name="companySize" required style="padding: 0.75rem; border: 1px solid var(--glass-border); border-radius: 8px; background: rgba(255,255,255,0.1); color: var(--text-primary); backdrop-filter: blur(10px);">
                    <option value="">Company Size</option>
                    <option value="1-50">1-50 employees</option>
                    <option value="51-200">51-200 employees</option>
                    <option value="201-1000">201-1000 employees</option>
                    <option value="1000+">1000+ employees</option>
                </select>
                <textarea name="message" placeholder="Tell us about your reconciliation needs..." rows="4" style="padding: 0.75rem; border: 1px solid var(--glass-border); border-radius: 8px; background: rgba(255,255,255,0.1); color: var(--text-primary); backdrop-filter: blur(10px); resize: vertical;"></textarea>

                <button type="submit" class="btn btn-primary" style="margin-top: 1rem;">Send Message</button>
            </form>

            <div id="contactSuccess" style="display: none; text-align: center; padding: 2rem;">
                <div style="font-size: 3rem; color: var(--emerald-primary); margin-bottom: 1rem;">✓</div>
                <h3 style="margin-bottom: 1rem; color: var(--emerald-primary);">Message Sent!</h3>
                <p style="color: var(--text-secondary);">Thank you for your interest. Our sales team will contact you within 24 hours.</p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <h3>ReconcilePro</h3>
                <p style="color: var(--text-secondary); margin-bottom: 1rem;">
                    The future of financial reconciliation.
                </p>
                <div style="display: flex; gap: 1rem;">
                    <a href="#" style="color: var(--text-secondary); font-size: 1.2rem;">📧</a>
                    <a href="#" style="color: var(--text-secondary); font-size: 1.2rem;">💬</a>
                    <a href="#" style="color: var(--text-secondary); font-size: 1.2rem;">📱</a>
                </div>
            </div>
            <div class="footer-section">
                <h3>Product</h3>
                <a href="#">Features</a>
                <a href="#">Pricing</a>
                <a href="#">Integrations</a>
                <a href="#">API Documentation</a>
                <a href="#">Security</a>
            </div>
            <div class="footer-section">
                <h3>Company</h3>
                <a href="#">About Us</a>
                <a href="#">Careers</a>
                <a href="#">Press</a>
                <a href="#">Partners</a>
                <a href="#">Contact</a>
            </div>
            <div class="footer-section">
                <h3>Resources</h3>
                <a href="#">Blog</a>
                <a href="#">Help Center</a>
                <a href="#">Webinars</a>
                <a href="#">Case Studies</a>
                <a href="#">Status Page</a>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2025 ReconcilePro. All rights reserved. | Privacy Policy | Terms of Service</p>
        </div>
    </footer>

    <script>
        // Define demoSection at the top so it's available everywhere
        const demoSection = document.getElementById('demo');
        // Make currentStep and demoInterval global
        let demoInterval;
        let currentStep = 1;

        // Navbar scroll effect
        window.addEventListener('scroll', () => {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Particle system
        function createParticles() {
            const particlesContainer = document.querySelector('.particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            createParticles();

            // Initialize demo
            showDemoStep(1);
            startDemoAutoAdvance(); // Always start auto-advance on load

            // Initialize feature showcase
            showFeature('dashboard');

            // Start feature auto-cycle when showcase comes into view
            const featureShowcase = document.querySelector('.feature-showcase');
            if (featureShowcase) {
                const showcaseObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            startFeatureCycle();
                        } else {
                            stopFeatureCycle();
                        }
                    });
                }, { threshold: 0.5 });
                showcaseObserver.observe(featureShowcase);
            }

            // Start counters when hero section is visible
            const heroSection = document.querySelector('.hero');
            if (heroSection) {
                const heroObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            startCounters();
                            heroObserver.unobserve(entry.target); // Only animate once
                        }
                    });
                }, { threshold: 0.3 });
                heroObserver.observe(heroSection);
            }

            // Start auto-advance for demo when it comes into view
            startDemoAutoAdvance(); // Always start auto-advance on load

            // Observe all sections for animation
            document.querySelectorAll('.section').forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(30px)';
                section.style.transition = 'all 0.8s ease';
                observer.observe(section);
            });

            // Add hover effects to cards
            document.querySelectorAll('.feature-card, .testimonial').forEach(card => {
                card.addEventListener('mouseenter', () => {
                    card.style.transform = 'translateY(-10px) scale(1.02)';
                });

                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add hover effects to demo steps
            document.querySelectorAll('.demo-step').forEach(step => {
                step.addEventListener('mouseenter', () => {
                    stopDemoAutoAdvance();
                });

                step.addEventListener('mouseleave', () => {
                    startDemoAutoAdvance(); // Always restart auto-advance on mouse leave
                });
            });

            // Contact form event listeners
            const contactForm = document.getElementById('contactForm');
            if (contactForm) {
                contactForm.addEventListener('submit', handleContactSubmit);
            }

            // Close modal when clicking outside
            const contactModal = document.getElementById('contactModal');
            if (contactModal) {
                contactModal.addEventListener('click', (e) => {
                    if (e.target === contactModal) {
                        closeContactModal();
                    }
                });
            }

            // Close modal with Escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && contactModal.style.display === 'block') {
                    closeContactModal();
                }
            });

            // Feature showcase interaction handlers
            const showcaseTabs = document.querySelectorAll('.showcase-tab');
            showcaseTabs.forEach(tab => {
                tab.addEventListener('mouseenter', () => {
                    stopFeatureCycle();
                });

                tab.addEventListener('mouseleave', () => {
                    if (featureShowcase && isElementInViewport(featureShowcase)) {
                        setTimeout(startFeatureCycle, 2000); // Restart after 2 seconds
                    }
                });
            });
        });

        // Helper function to check if element is in viewport
        function isElementInViewport(el) {
            if (!el) return false;
            const rect = el.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        }

        // Interactive Demo Functions
        function showDemoStep(step) {
            // Hide all demo content
            document.querySelectorAll('.demo-content').forEach(content => {
                content.style.display = 'none';
            });

            // Remove active class from all demo steps
            document.querySelectorAll('.demo-step').forEach(stepEl => {
                stepEl.style.transform = 'scale(1)';
                stepEl.style.borderColor = 'transparent';
            });

            // Show selected demo content
            const selectedContent = document.getElementById(`demo-step-${step}`);
            if (selectedContent) {
                selectedContent.style.display = 'block';
            }

            // Highlight selected step
            const selectedStep = document.querySelectorAll('.demo-step')[step - 1];
            if (selectedStep) {
                selectedStep.style.transform = 'scale(1.05)';
                selectedStep.style.borderColor = 'var(--neon-blue)';
            }

            // Update currentStep for auto-advance
            currentStep = step;
        }

        function resetDemo() {
            showDemoStep(1);
        }

        // Auto-advance demo steps
        function startDemoAutoAdvance() {
            stopDemoAutoAdvance(); // Prevent multiple intervals
            demoInterval = setInterval(() => {
                currentStep = currentStep >= 3 ? 1 : currentStep + 1;
                showDemoStep(currentStep);
            }, 3000); // 3 seconds
        }

        function stopDemoAutoAdvance() {
            if (demoInterval) {
                clearInterval(demoInterval);
                demoInterval = null;
            }
        }

        // Contact Modal Functions
        function openContactModal() {
            document.getElementById('contactModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeContactModal() {
            document.getElementById('contactModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            // Reset form
            document.getElementById('contactForm').style.display = 'flex';
            document.getElementById('contactSuccess').style.display = 'none';
            document.getElementById('contactForm').reset();
        }

        // Handle contact form submission
        function handleContactSubmit(event) {
            event.preventDefault();

            // Show loading state
            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;

            // Simulate form submission (replace with actual form handling)
            setTimeout(() => {
                document.getElementById('contactForm').style.display = 'none';
                document.getElementById('contactSuccess').style.display = 'block';

                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;

                // Auto-close modal after 3 seconds
                setTimeout(() => {
                    closeContactModal();
                }, 3000);
            }, 1500);
        }

        // Animated Counter Function
        function animateCounter(element, target, suffix = '', duration = 2000) {
            const start = 0;
            const increment = target / (duration / 16); // 60fps
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                // Format the number based on suffix
                let displayValue;
                if (suffix === 'M+') {
                    displayValue = current.toFixed(0) + suffix;
                } else if (suffix === '%') {
                    displayValue = current.toFixed(1) + suffix;
                } else {
                    displayValue = Math.floor(current) + suffix;
                }

                element.textContent = displayValue;
            }, 16);
        }

        // Start counters when hero section is visible
        function startCounters() {
            const statNumbers = document.querySelectorAll('.stat-number[data-target]');
            statNumbers.forEach(stat => {
                const target = parseFloat(stat.dataset.target);
                const suffix = stat.dataset.suffix || '';
                animateCounter(stat, target, suffix);
            });
        }

        // Mobile Menu Functions
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            const isVisible = mobileMenu.style.display === 'block';
            mobileMenu.style.display = isVisible ? 'none' : 'block';

            // Update button icon
            const btn = document.querySelector('.mobile-menu-btn');
            btn.textContent = isVisible ? '☰' : '✕';
        }

        function closeMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.style.display = 'none';

            // Reset button icon
            const btn = document.querySelector('.mobile-menu-btn');
            btn.textContent = '☰';
        }

        // Feature Showcase Functions
        function showFeature(featureName) {
            // Hide all feature views
            document.querySelectorAll('.feature-view').forEach(view => {
                view.style.display = 'none';
            });

            // Remove active class from all tabs
            document.querySelectorAll('.showcase-tab').forEach(tab => {
                tab.style.background = 'transparent';
                tab.style.color = 'var(--text-secondary)';
            });

            // Show selected feature view
            const selectedView = document.getElementById(`feature-${featureName}`);
            if (selectedView) {
                selectedView.style.display = 'block';
            }

            // Highlight selected tab
            const selectedTab = event.target;
            selectedTab.style.background = 'var(--emerald-primary)';
            selectedTab.style.color = 'white';
        }

        // Auto-cycle through features
        let featureCycleInterval;
        function startFeatureCycle() {
            const features = ['dashboard', 'upload', 'results'];
            let currentIndex = 0;

            featureCycleInterval = setInterval(() => {
                currentIndex = (currentIndex + 1) % features.length;
                const feature = features[currentIndex];

                // Simulate tab click
                const tab = document.querySelector(`[onclick="showFeature('${feature}')"]`);
                if (tab) {
                    showFeature(feature);
                    // Update tab appearance
                    document.querySelectorAll('.showcase-tab').forEach(t => {
                        t.style.background = 'transparent';
                        t.style.color = 'var(--text-secondary)';
                    });
                    tab.style.background = 'var(--emerald-primary)';
                    tab.style.color = 'white';
                }
            }, 4000);
        }

        function stopFeatureCycle() {
            if (featureCycleInterval) {
                clearInterval(featureCycleInterval);
            }
        }
    </script>
</body>
</html>