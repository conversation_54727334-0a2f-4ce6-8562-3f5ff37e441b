<div class="max-w-7xl mx-auto p-6">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Reconciliation Summary</h1>
        <p class="text-gray-600">Comprehensive overview of your reconciliation activities and performance metrics</p>
        <%= if @selected_run_id do %>
          <% selected_run = Enum.find(@reconciliation_runs, &(&1.id == String.to_integer(@selected_run_id))) %>
          <%= if selected_run do %>
            <div class="mt-2 inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
              <.icon name="hero-funnel" class="w-4 h-4 mr-1" />
              Filtered by: <%= selected_run.name %>
            </div>
          <% end %>
        <% end %>
      </div>
      
      <!-- Run Filter -->
      <div class="flex items-center space-x-4">
        <!-- Specific Run Filter -->
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700">Specific Run:</label>
          <form phx-change="filter_by_run" class="inline">
            <select
              name="run_id"
              class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 min-w-[300px] max-w-[400px]"
            >
              <option value="" selected={@selected_run_id == nil}>
                <%= if length(@reconciliation_runs) == 0 do %>
                  No runs available
                <% else %>
                  All Runs (<%= length(@reconciliation_runs) %> available)
                <% end %>
              </option>
              <%= for run <- @reconciliation_runs do %>
                <option value={run.id} selected={@selected_run_id == to_string(run.id)}>
                  <%= run.name %> | <%= format_date(run.inserted_at) %> | <%= String.upcase(run.status) %>
                  <%= if run.match_rate do %>
                    | <%= Decimal.to_string(run.match_rate, :normal) %>% match
                  <% end %>
                </option>
              <% end %>
            </select>
          </form>
          <%= if length(@reconciliation_runs) >= 100 do %>
            <span class="text-xs text-gray-500">Showing recent 100 runs</span>
          <% end %>
        </div>

        <!-- Refresh Button -->
        <button
          phx-click="refresh_data"
          class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-lg text-sm font-medium flex items-center"
          title="Refresh data"
        >
          <.icon name="hero-arrow-path" class="w-4 h-4" />
        </button>
      </div>
    </div>
  </div>

  <%= if @loading do %>
    <!-- Loading State -->
    <div class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      <span class="ml-3 text-gray-600">Loading summary data...</span>
    </div>
  <% else %>
    <%= if has_data?(@summary_data) do %>
      <!-- Key Metrics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Files Uploaded -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <.icon name="hero-document-arrow-up" class="w-6 h-6 text-blue-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Files Uploaded</p>
              <p class="text-2xl font-bold text-gray-900"><%= format_number(@summary_data.file_upload_stats.total_files) %></p>
            </div>
          </div>
        </div>

        <!-- Total Transactions -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <.icon name="hero-banknotes" class="w-6 h-6 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Transactions</p>
              <p class="text-2xl font-bold text-gray-900"><%= format_number(@summary_data.transaction_metrics.total_entries_processed) %></p>
            </div>
          </div>
        </div>

        <!-- Match Rate -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
              <.icon name="hero-check-circle" class="w-6 h-6 text-emerald-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Overall Match Rate</p>
              <p class="text-2xl font-bold text-gray-900"><%= format_percentage(@summary_data.transaction_metrics.match_rate_percentage) %></p>
            </div>
          </div>
        </div>

        <!-- Completed Runs -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <.icon name="hero-chart-bar" class="w-6 h-6 text-purple-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Completed Runs</p>
              <p class="text-2xl font-bold text-gray-900"><%= format_number(@summary_data.summary_info.completed_runs) %></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed Statistics Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- File Upload Statistics -->
        <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">File Upload Statistics</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Total Files</span>
                <span class="font-medium text-gray-900"><%= format_number(@summary_data.file_upload_stats.total_files) %></span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Total Size</span>
                <span class="font-medium text-gray-900"><%= format_file_size(@summary_data.file_upload_stats.total_size) %></span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Average File Size</span>
                <span class="font-medium text-gray-900"><%= format_file_size(@summary_data.file_upload_stats.avg_file_size) %></span>
              </div>
            </div>
            
            <!-- File Type Breakdown -->
            <%= if map_size(@summary_data.file_upload_stats.file_type_breakdown) > 0 do %>
              <div class="mt-6 pt-6 border-t border-gray-200">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Uploaded Files Breakdown</h4>
                <div class="space-y-3">
                  <%= for {file_type, stats} <- @summary_data.file_upload_stats.file_type_breakdown do %>
                    <div class="border border-gray-200 rounded-lg p-3">
                      <div class="flex justify-between items-center mb-2">
                        <span class="text-gray-600 font-medium"><%= file_type_display(file_type) %></span>
                        <span class="font-medium text-gray-900"><%= format_number(stats.count) %> files</span>
                      </div>
                      <%= if Map.has_key?(stats, :sample_names) and length(stats.sample_names) > 0 do %>
                        <div class="text-xs text-gray-500">
                          <span class="font-medium"></span>
                          <%= Enum.join(stats.sample_names, ", ") %>
                          <%= if stats.count > length(stats.sample_names) do %>
                            <span class="text-gray-400">and <%= stats.count - length(stats.sample_names) %> more...</span>
                          <% end %>
                        </div>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Transaction Matching Metrics -->
        <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Transaction Matching Metrics</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <!-- File Type Processing Counts -->
              <div class="bg-gray-50 rounded-lg p-4 mb-4">
                <h4 class="text-sm font-medium text-gray-700 mb-3">Processed by File Type</h4>
                <div class="grid grid-cols-2 gap-4">
                  <div class="flex justify-between items-center">
                    <div class="flex items-center">
                      <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                      <div class="flex flex-col">
                        <span class="text-sm text-gray-600">
                          <%= if @summary_data.transaction_metrics.sample_file_a_name do %>
                            <%= @summary_data.transaction_metrics.sample_file_a_name %>
                          <% else %>
                            First Files (A)
                          <% end %>
                        </span>
                      </div>
                    </div>
                    <span class="font-medium text-blue-600"><%= format_number(@summary_data.transaction_metrics.total_processed_a) %></span>
                  </div>
                  <div class="flex justify-between items-center">
                    <div class="flex items-center">
                      <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                      <div class="flex flex-col">
                        <span class="text-sm text-gray-600">
                          <%= if @summary_data.transaction_metrics.sample_file_b_name do %>
                            <%= @summary_data.transaction_metrics.sample_file_b_name %>
                          <% else %>
                            Second Files (B)
                          <% end %>
                        </span>
                      </div>
                    </div>
                    <span class="font-medium text-green-600"><%= format_number(@summary_data.transaction_metrics.total_processed_b) %></span>
                  </div>
                </div>
              </div>

              <!-- Overall Metrics -->
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Total Processed</span>
                <span class="font-medium text-gray-900"><%= format_number(@summary_data.transaction_metrics.total_entries_processed) %></span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Successfully Matched</span>
                <span class="font-medium text-green-600"><%= format_number(@summary_data.transaction_metrics.total_entries_matched) %></span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Unmatched</span>
                <span class="font-medium text-red-600"><%= format_number(@summary_data.transaction_metrics.total_entries_unmatched) %></span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Match Rate</span>
                <span class="font-medium text-gray-900"><%= format_percentage(@summary_data.transaction_metrics.match_rate_percentage) %></span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Average Match Rate</span>
                <span class="font-medium text-gray-900"><%= format_percentage(@summary_data.transaction_metrics.avg_match_rate) %></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8">
        <!-- Status Overview - COMMENTED OUT -->
        <%!-- <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Status Overview</h3>
          </div>
          <div class="p-6">
            <div class="space-y-3">
              <%= for {status, count} <- @summary_data.status_overview.status_breakdown do %>
                <div class="flex justify-between items-center">
                  <div class="flex items-center">
                    <span class={["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium", status_badge_class(status)]}>
                      <%= String.capitalize(status) %>
                    </span>
                  </div>
                  <span class="font-medium text-gray-900"><%= format_number(count) %></span>
                </div>
              <% end %>
            </div>
            <div class="mt-4 pt-4 border-t border-gray-200">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Success Rate</span>
                <span class="font-medium text-gray-900"><%= format_percentage(@summary_data.status_overview.completion_rate) %></span>
              </div>
            </div>
          </div>
        </div> --%>

        <!-- Currency Breakdown -->
        <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div class="px-4 py-3 border-b border-gray-200">
            <h3 class="text-base font-medium text-gray-900">Currency Breakdown</h3>
          </div>
          <div class="p-4">
            <%= if length(@summary_data.currency_breakdown.currencies_found) > 0 do %>
              <!-- Compact Currency Table -->
              <div class="overflow-x-auto">
                <table class="min-w-full text-sm">
                  <thead>
                    <tr class="border-b border-gray-200">
                      <th class="text-left py-2 text-xs font-medium text-gray-500 uppercase">Currency</th>
                      <th class="text-right py-2 text-xs font-medium text-gray-500 uppercase">File A</th>
                      <th class="text-right py-2 text-xs font-medium text-gray-500 uppercase">File B</th>
                      <th class="text-right py-2 text-xs font-medium text-gray-500 uppercase">Total</th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-100">
                    <%= for {currency, stats} <- @summary_data.currency_breakdown.currency_stats do %>
                      <tr class="hover:bg-gray-50">
                        <td class="py-2 font-medium text-gray-900">
                          <div class="flex items-center">
                            <div class="w-2 h-2 rounded-full mr-2 bg-indigo-500"></div>
                            <%= currency %>
                          </div>
                        </td>
                        <td class="py-2 text-right text-gray-900">
                          <div class="font-medium text-blue-700"><%= format_currency_with_symbol(stats.file_a_amount, currency) %></div>
                          <div class="text-xs text-gray-500"><%= format_number(stats.file_a_count) %> txns</div>
                        </td>
                        <td class="py-2 text-right text-gray-900">
                          <div class="font-medium text-green-700"><%= format_currency_with_symbol(stats.file_b_amount, currency) %></div>
                          <div class="text-xs text-gray-500"><%= format_number(stats.file_b_count) %> txns</div>
                        </td>
                        <td class="py-2 text-right text-gray-900">
                          <div class="font-medium"><%= format_currency_with_symbol(stats.total_amount, currency) %></div>
                          <div class="text-xs text-gray-500"><%= format_number(stats.transaction_count) %> txns</div>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            <% else %>
              <div class="text-center py-6">
                <div class="text-gray-400 mb-2">
                  <svg class="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                  </svg>
                </div>
                <p class="text-sm text-gray-500">No currency data available</p>
                <p class="text-xs text-gray-400 mt-1">Upload files with currency information</p>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Reconciliation Status Summary - COMMENTED OUT -->
        <%!-- <%= if length(@summary_data.currency_breakdown.currencies_found) > 0 do %>
          <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">Reconciliation Status</h3>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Perfect Matches -->
                <div class="text-center p-4 bg-green-50 rounded-lg">
                  <div class="text-2xl font-bold text-green-600">
                    <%= @summary_data.currency_breakdown.currency_stats
                        |> Enum.count(fn {_currency, stats} ->
                          Decimal.equal?(stats.difference, Decimal.new("0"))
                        end) %>
                  </div>
                  <div class="text-sm text-green-700 font-medium">Perfect Matches</div>
                  <div class="text-xs text-green-600">No differences found</div>
                </div>

                <!-- Minor Differences -->
                <div class="text-center p-4 bg-yellow-50 rounded-lg">
                  <div class="text-2xl font-bold text-yellow-600">
                    <%= @summary_data.currency_breakdown.currency_stats
                        |> Enum.count(fn {_currency, stats} ->
                          not Decimal.equal?(stats.difference, Decimal.new("0")) and
                          Decimal.lt?(stats.difference_percentage, Decimal.new("5"))
                        end) %>
                  </div>
                  <div class="text-sm text-yellow-700 font-medium">Minor Differences</div>
                  <div class="text-xs text-yellow-600">&lt; 5% variance</div>
                </div>

                <!-- Major Differences -->
                <div class="text-center p-4 bg-red-50 rounded-lg">
                  <div class="text-2xl font-bold text-red-600">
                    <%= @summary_data.currency_breakdown.currency_stats
                        |> Enum.count(fn {_currency, stats} ->
                          Decimal.gte?(stats.difference_percentage, Decimal.new("5"))
                        end) %>
                  </div>
                  <div class="text-sm text-red-700 font-medium">Major Differences</div>
                  <div class="text-xs text-red-600">&ge; 5% variance</div>
                </div>
              </div>

              <!-- Overall Status -->
              <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="text-center">
                  <%= if Enum.all?(@summary_data.currency_breakdown.currency_stats, fn {_currency, stats} ->
                        Decimal.equal?(stats.difference, Decimal.new("0"))
                      end) do %>
                    <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                      <.icon name="hero-check-circle" class="w-4 h-4 mr-1" />
                      All Currencies Reconciled
                    </div>
                  <% else %>
                    <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                      <.icon name="hero-exclamation-triangle" class="w-4 h-4 mr-1" />
                      Differences Found - Review Required
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %> --%>

        <!-- Currency Reconciliation Summary - Full Width -->
      </div>

      <!-- Currency Reconciliation Summary (Full Width) -->
      <%= if length(@summary_data.currency_breakdown.currencies_found) > 0 do %>
        <div class="bg-white rounded-lg shadow-sm border overflow-hidden mb-8">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium text-gray-900">Currency Reconciliation Summary</h3>
              <span class="text-sm text-gray-500"><%= length(@summary_data.currency_breakdown.currencies_found) %> currencies</span>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Currency</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">File A</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">File B</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Difference</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <%= for {currency, stats} <- @summary_data.currency_breakdown.currency_stats do %>
                  <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                          <span class="text-xs font-medium text-gray-600"><%= currency %></span>
                        </div>
                        <div class="ml-3">
                          <div class="text-sm font-medium text-gray-900"><%= currency %></div>
                          <div class="text-sm text-gray-500"><%= format_number(stats.transaction_count) %> transactions</div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right">
                      <div class="text-sm font-medium text-blue-700"><%= format_currency_with_symbol(stats.file_a_amount, currency) %></div>
                      <div class="text-sm text-gray-500"><%= format_number(stats.file_a_count) %> txns</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right">
                      <div class="text-sm font-medium text-green-700"><%= format_currency_with_symbol(stats.file_b_amount, currency) %></div>
                      <div class="text-sm text-gray-500"><%= format_number(stats.file_b_count) %> txns</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right">
                      <div class={["text-sm font-medium", difference_color(stats.difference)]}>
                        <%= format_difference_with_symbol(stats.difference, currency) %>
                      </div>
                      <%= if not Decimal.equal?(stats.difference_percentage, Decimal.new("0")) do %>
                        <div class={["text-xs", difference_color(stats.difference)]}>
                          <%= Decimal.to_string(stats.difference_percentage, :normal) %>%
                        </div>
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right">
                      <%= if Decimal.equal?(stats.difference, Decimal.new("0")) do %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                          </svg>
                          Matched
                        </span>
                      <% else %>
                        <% abs_percentage = Decimal.abs(stats.difference_percentage) %>
                        <%= if Decimal.lt?(abs_percentage, Decimal.new("5")) do %>
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            Minor
                          </span>
                        <% else %>
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                            Major
                          </span>
                        <% end %>
                      <% end %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
          <%= if @summary_data.currency_breakdown.primary_currency do %>
            <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
              <div class="flex justify-between items-center text-sm">
                <span class="text-gray-600">Primary Currency</span>
                <span class="font-medium text-gray-900"><%= @summary_data.currency_breakdown.primary_currency %></span>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>

      <!-- Remaining sections in grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">

        <!-- Data Range - COMMENTED OUT -->
        <%!-- <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Data Range</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <div>
                <span class="text-sm text-gray-600">Period</span>
                <p class="font-medium text-gray-900">All Time</p>
              </div>
              <%= if @summary_data.date_range.earliest do %>
                <div>
                  <span class="text-sm text-gray-600">Earliest Record</span>
                  <p class="font-medium text-gray-900"><%= format_date(@summary_data.date_range.earliest) %></p>
                </div>
                <div>
                  <span class="text-sm text-gray-600">Latest Record</span>
                  <p class="font-medium text-gray-900"><%= format_date(@summary_data.date_range.latest) %></p>
                </div>
              <% else %>
                <div class="text-center py-4">
                  <p class="text-sm text-gray-500">No date range available</p>
                </div>
              <% end %>
            </div>
          </div>
        </div> --%>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div class="flex flex-wrap gap-4">
          <.link
            navigate={~p"/reconciliation"}
            class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium flex items-center"
          >
            <.icon name="hero-plus" class="w-4 h-4 mr-2" />
            New Reconciliation
          </.link>
          <.link
            navigate={~p"/reports"}
            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium flex items-center"
          >
            <.icon name="hero-chart-bar" class="w-4 h-4 mr-2" />
            View Reports
          </.link>
          <.link
            navigate={~p"/transactions"}
            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium flex items-center"
          >
            <.icon name="hero-banknotes" class="w-4 h-4 mr-2" />
            View Transactions
          </.link>
        </div>
      </div>

    <% else %>
      <!-- Empty State -->
      <div class="text-center py-12">
        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <.icon name="hero-chart-bar" class="w-12 h-12 text-gray-400" />
        </div>
        <h3 class="text-xl font-medium text-gray-900 mb-2">No Reconciliation Data</h3>
        <p class="text-gray-500 mb-8 max-w-md mx-auto">
          You haven't created any reconciliation runs yet. Start by uploading your first set of files to see comprehensive summary statistics here.
        </p>
        <.link
          navigate={~p"/reconciliation"}
          class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center"
        >
          <.icon name="hero-plus" class="w-5 h-5 mr-2" />
          Create Your First Reconciliation
        </.link>
      </div>
    <% end %>
  <% end %>
</div>
