# Currency Differences Feature

## Overview

The enhanced reconciliation system now displays **differences between File A and File B** for each currency, providing detailed insights into discrepancies at the currency level.

## What's New

### 1. **Currency-Level Difference Calculation**
- Shows the exact difference between File A and File B amounts for each currency
- Calculates percentage variance to identify significant discrepancies
- Color-coded indicators for easy identification

### 2. **Enhanced Summary Display**
The summary page now shows:
- **File A Amount** - Total amount from File A for each currency
- **File B Amount** - Total amount from File B for each currency  
- **Difference** - File A amount minus File B amount
- **Percentage Difference** - Relative variance between the files
- **Transaction Counts** - Number of transactions per file per currency

### 3. **Visual Indicators**
- **Green Background**: Perfect match (no difference)
- **Yellow Background**: Minor differences (< 5% variance)
- **Red Background**: Major differences (≥ 5% variance)

### 4. **Reconciliation Status Dashboard**
- **Perfect Matches**: Currencies with zero differences
- **Minor Differences**: Currencies with < 5% variance
- **Major Differences**: Currencies with ≥ 5% variance
- **Overall Status**: Quick assessment of reconciliation health

## Example Output

```
Currency Reconciliation Summary

USD
Difference: +$25.00 (0.48%)
File A: $5,266.25 (5 transactions)
File B: $5,291.25 (5 transactions)
Combined Total: $10,557.50 (10 transactions)

EUR  
Difference: -€200.00 (1.96%)
File A: €10,326.05 (5 transactions)
File B: €10,126.05 (5 transactions)
Combined Total: €20,452.10 (10 transactions)

GBP
Difference: +£550.00 (5.52%)
File A: £9,713.25 (5 transactions)
File B: £10,263.25 (6 transactions)
Combined Total: £19,976.50 (11 transactions)
```

## Benefits

### **For Accountants & Finance Teams:**
- **Quick Identification**: Instantly spot which currencies have discrepancies
- **Variance Analysis**: Understand the magnitude of differences with percentages
- **Drill-Down Capability**: See transaction counts to understand data volume
- **Priority Assessment**: Focus on major differences first

### **For Auditors:**
- **Comprehensive View**: See all currency reconciliations in one place
- **Variance Thresholds**: Easily identify differences above materiality thresholds
- **Documentation**: Clear difference calculations for audit trails

### **For Data Analysts:**
- **Pattern Recognition**: Identify systematic differences across currencies
- **Trend Analysis**: Monitor reconciliation quality over time
- **Exception Reporting**: Focus on currencies with significant variances

## Technical Implementation

### **Database Changes:**
- Enhanced `calculate_currency_breakdown/1` function
- Added `calculate_currency_differences/1` helper function
- New percentage calculation logic

### **UI Enhancements:**
- Updated summary template with difference display
- Added color-coded status indicators
- Responsive grid layout for currency breakdown

### **Calculation Logic:**
```elixir
difference = file_a_amount - file_b_amount
percentage = abs(difference) / max(abs(file_a_amount), abs(file_b_amount)) * 100
```

## Demo Files

Use the demo files to test the feature:
- `test/fixtures/reconciliation_test_data/csv/DEMO_FILE_A.csv`
- `test/fixtures/reconciliation_test_data/csv/DEMO_FILE_B.csv`

These files contain intentional differences across USD, EUR, and GBP currencies to demonstrate the feature.

## Future Enhancements

1. **Threshold Configuration**: Allow users to set custom variance thresholds
2. **Difference Alerts**: Email notifications for significant discrepancies
3. **Historical Trending**: Track difference patterns over time
4. **Export Functionality**: Export difference reports to Excel/PDF
5. **Drill-Down Analysis**: Click to see specific transactions causing differences
