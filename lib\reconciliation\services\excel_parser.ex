defmodule Reconciliation.Services.ExcelParser do
  @moduledoc """
  Service for parsing Excel and CSV files and extracting transaction data.
  """

  alias Reconciliation.{
    UploadedFile
    # Transaction # This alias appears to be unused in this module
  }

  require Logger

  @doc """
  Parses an uploaded file and extracts transaction data.
  """
  def parse_file(%UploadedFile{} = uploaded_file) do
    Logger.info("Starting to parse file: #{uploaded_file.filename}")

    # Update status to processing
    {:ok, uploaded_file} = Reconciliation.update_uploaded_file(uploaded_file, %{
      status: "processing"
    })

    try do
      case get_file_type(uploaded_file) do
        :excel -> parse_excel_file(uploaded_file)
        :csv -> parse_csv_file(uploaded_file)
        :unknown -> {:error, "Unsupported file format"}
      end
    rescue
      error ->
        Logger.error("Error parsing file #{uploaded_file.filename}: #{inspect(error)}")
        Reconciliation.mark_file_failed(uploaded_file, [Exception.message(error)])
        {:error, Exception.message(error)}
    end
  end

  @doc """
  Parse uploaded file with progress tracking.
  """
  def parse_file_with_progress(%UploadedFile{} = uploaded_file, progress_callback) do
    Logger.info("Starting to parse file with progress: #{uploaded_file.filename}")

    # Update status to processing
    {:ok, uploaded_file} = Reconciliation.update_uploaded_file(uploaded_file, %{
      status: "processing"
    })

    try do
      case get_file_type(uploaded_file) do
        :excel -> parse_excel_file_with_progress(uploaded_file, progress_callback)
        :csv -> parse_csv_file_with_progress(uploaded_file, progress_callback)
        :unknown -> {:error, "Unsupported file format"}
      end
    rescue
      error ->
        Logger.error("Error parsing file #{uploaded_file.filename}: #{inspect(error)}")
        Reconciliation.mark_file_failed(uploaded_file, [Exception.message(error)])
        {:error, Exception.message(error)}
    end
  end

  @doc """
  Determines the file type based on extension.
  """
  def get_file_type(%UploadedFile{filename: filename}) do
    case Path.extname(filename) |> String.downcase() do
      ext when ext in [".xlsx", ".xls"] -> :excel
      ".csv" -> :csv
      _ -> :unknown
    end
  end

  # Parse Excel files using xlsxir
  defp parse_excel_file(%UploadedFile{} = uploaded_file) do
    Logger.info("Parsing Excel file: #{uploaded_file.filename}")

    case Xlsxir.multi_extract(uploaded_file.file_path) do
      {:ok, table_ids} ->
        # Use the first sheet
        [table_id | _] = table_ids

        # Get all rows
        rows = Xlsxir.get_list(table_id)

        # Clean up
        Xlsxir.close(table_id)

        process_rows(uploaded_file, rows)

      {:error, reason} ->
        error_msg = "Failed to parse Excel file: #{inspect(reason)}"
        Logger.error(error_msg)
        Reconciliation.mark_file_failed(uploaded_file, [error_msg])
        {:error, error_msg}
    end
  end

  # Parse Excel files with progress tracking
  defp parse_excel_file_with_progress(%UploadedFile{} = uploaded_file, progress_callback) do
    Logger.info("Parsing Excel file: #{uploaded_file.filename}")

    progress_callback.(%{
      status: "parsing",
      progress: 10,
      message: "Reading Excel file..."
    })

    case Xlsxir.multi_extract(uploaded_file.file_path) do
      {:ok, table_ids} ->
        progress_callback.(%{
          status: "parsing",
          progress: 30,
          message: "Extracting worksheet data..."
        })

        # Use the first sheet
        [table_id | _] = table_ids

        # Get all rows
        rows = Xlsxir.get_list(table_id)

        # Clean up
        Xlsxir.close(table_id)

        progress_callback.(%{
          status: "parsing",
          progress: 50,
          message: "Processing rows..."
        })

        process_rows_with_progress(uploaded_file, rows, progress_callback)

      {:error, reason} ->
        error_msg = "Failed to parse Excel file: #{inspect(reason)}"
        Logger.error(error_msg)
        Reconciliation.mark_file_failed(uploaded_file, [error_msg])
        {:error, error_msg}
    end
  end

  # Parse CSV files using NimbleCSV
  defp parse_csv_file(%UploadedFile{} = uploaded_file) do
    Logger.info("Parsing CSV file: #{uploaded_file.filename}")

    case File.read(uploaded_file.file_path) do
      {:ok, content} ->
        # Define CSV parser
        alias NimbleCSV.RFC4180, as: CSV

        rows = content
        |> CSV.parse_string(skip_headers: false)
        |> Enum.to_list()

        process_rows(uploaded_file, rows)

      {:error, reason} ->
        error_msg = "Failed to read CSV file: #{inspect(reason)}"
        Logger.error(error_msg)
        Reconciliation.mark_file_failed(uploaded_file, [error_msg])
        {:error, error_msg}
    end
  end

  # Parse CSV files with progress tracking
  defp parse_csv_file_with_progress(%UploadedFile{} = uploaded_file, progress_callback) do
    Logger.info("Parsing CSV file: #{uploaded_file.filename}")

    progress_callback.(%{
      status: "parsing",
      progress: 10,
      message: "Reading CSV file..."
    })

    case File.read(uploaded_file.file_path) do
      {:ok, content} ->
        progress_callback.(%{
          status: "parsing",
          progress: 30,
          message: "Parsing CSV content..."
        })

        # Define CSV parser
        alias NimbleCSV.RFC4180, as: CSV

        rows = content
        |> CSV.parse_string(skip_headers: false)
        |> Enum.to_list()

        progress_callback.(%{
          status: "parsing",
          progress: 50,
          message: "Processing rows..."
        })

        process_rows_with_progress(uploaded_file, rows, progress_callback)

      {:error, reason} ->
        error_msg = "Failed to read CSV file: #{inspect(reason)}"
        Logger.error(error_msg)
        Reconciliation.mark_file_failed(uploaded_file, [error_msg])
        {:error, error_msg}
    end
  end

  # Process rows and extract transaction data
  defp process_rows(%UploadedFile{} = uploaded_file, []) do
    error_msg = "File is empty or contains no data"
    Reconciliation.mark_file_failed(uploaded_file, [error_msg])
    {:error, error_msg}
  end

  defp process_rows(%UploadedFile{} = uploaded_file, [headers | data_rows]) do
    Logger.info("Processing #{length(data_rows)} data rows")

    # Separate transaction rows from summary rows
    {transaction_rows, summary_rows} = separate_transaction_and_summary_rows(data_rows)

    Logger.info("Found #{length(transaction_rows)} transaction rows and #{length(summary_rows)} summary rows")

    # Detect column mapping
    column_mapping = detect_column_mapping(headers)

    # Extract summary data from summary rows
    summary_data = extract_summary_data(summary_rows)

    # Update file with detected headers and mapping
    {:ok, uploaded_file} = Reconciliation.update_uploaded_file(uploaded_file, %{
      headers_detected: headers,
      column_mapping: column_mapping,
      total_rows: length(transaction_rows)
    })

    # Process each transaction row
    {transactions, errors} = process_data_rows(transaction_rows, column_mapping, uploaded_file)

    # Calculate summary statistics from processed transactions
    calculated_summary = calculate_summary_statistics(transactions)

    # Validate summary data
    validation_result = validate_summary_data(summary_data, calculated_summary)

    # Update file status and summary data
    final_status = if length(errors) == length(transaction_rows), do: "failed", else: "processed"

    summary_update_attrs = Map.merge(summary_data, calculated_summary)
    |> Map.merge(validation_result)
    |> Map.merge(%{
      status: final_status,
      processed_rows: length(transactions),
      error_rows: length(errors),
      processing_errors: errors,
      processed_at: DateTime.utc_now()
    })

    {:ok, _} = Reconciliation.update_uploaded_file(uploaded_file, summary_update_attrs)

    if final_status == "processed" do
      Logger.info("Successfully processed #{length(transactions)} transactions")
      {:ok, transactions}
    else
      Logger.error("Failed to process any transactions from file")
      {:error, "Failed to process transactions: #{Enum.join(errors, ", ")}"}
    end
  end

  # Process rows with progress tracking
  defp process_rows_with_progress(%UploadedFile{} = uploaded_file, [], _progress_callback) do
    error_msg = "File is empty or contains no data"
    Reconciliation.mark_file_failed(uploaded_file, [error_msg])
    {:error, error_msg}
  end

  defp process_rows_with_progress(%UploadedFile{} = uploaded_file, [headers | data_rows], progress_callback) do
    total_rows = length(data_rows)
    Logger.info("Processing #{total_rows} data rows")

    # Separate transaction rows from summary rows
    {transaction_rows, summary_rows} = separate_transaction_and_summary_rows(data_rows)
    transaction_count = length(transaction_rows)

    Logger.info("Found #{transaction_count} transaction rows and #{length(summary_rows)} summary rows")

    progress_callback.(%{
      status: "processing",
      progress: 60,
      message: "Detecting column mapping...",
      total_records: transaction_count,
      records_processed: 0
    })

    # Detect column mapping
    column_mapping = detect_column_mapping(headers)

    # Extract summary data from summary rows
    summary_data = extract_summary_data(summary_rows)

    # Update file with detected headers and mapping
    {:ok, uploaded_file} = Reconciliation.update_uploaded_file(uploaded_file, %{
      headers_detected: headers,
      column_mapping: column_mapping,
      total_rows: transaction_count
    })

    progress_callback.(%{
      status: "processing",
      progress: 70,
      message: "Processing transaction data...",
      total_records: transaction_count,
      records_processed: 0
    })

    # Process each transaction row with progress tracking
    {transactions, errors} = process_data_rows_with_progress(transaction_rows, column_mapping, uploaded_file, progress_callback)

    # Calculate summary statistics from processed transactions
    calculated_summary = calculate_summary_statistics(transactions)

    # Validate summary data
    validation_result = validate_summary_data(summary_data, calculated_summary)

    # Update file status and summary data
    final_status = if length(errors) == length(transaction_rows), do: "failed", else: "processed"

    summary_update_attrs = Map.merge(summary_data, calculated_summary)
    |> Map.merge(validation_result)
    |> Map.merge(%{
      status: final_status,
      processed_rows: length(transactions),
      error_rows: length(errors),
      processing_errors: errors,
      processed_at: DateTime.utc_now()
    })

    {:ok, _} = Reconciliation.update_uploaded_file(uploaded_file, summary_update_attrs)

    if final_status == "processed" do
      Logger.info("Successfully processed #{length(transactions)} transactions")
      {:ok, %{
        transactions: transactions,
        rows_inserted: length(transactions),
        processing_time: 0  # This will be updated by the progress callback
      }}
    else
      Logger.error("Failed to process any transactions from file")
      {:error, "Failed to process transactions: #{Enum.join(errors, ", ")}"}
    end
  end

  # Detect column mapping based on headers
  defp detect_column_mapping(headers) do
    headers
    |> Enum.with_index()
    |> Enum.reduce(%{}, fn {header, index}, acc ->
      field = detect_field_type(header)
      if field, do: Map.put(acc, field, index), else: acc
    end)
  end

  # Detect field type based on header name
  defp detect_field_type(header) when is_binary(header) do
    header_lower = String.downcase(header)

    cond do
      header_lower in ["date", "transaction_date", "date_of_transaction", "trans_date"] -> "date"
      header_lower in ["amount", "transaction_amount", "amt", "value"] -> "amount"
      header_lower in ["debit"] -> "debit"
      header_lower in ["credit"] -> "credit"
      header_lower in ["reference", "ref", "transaction_reference", "reference_number", "ref_no"] -> "reference"
      header_lower in ["description", "details", "transaction_details", "memo", "narration"] -> "description"
      header_lower in ["id", "transaction_id", "unique_id", "trans_id"] -> "transaction_id"
      header_lower in ["type", "transaction_type", "debit_credit", "dr_cr"] -> "type"
      header_lower in ["account", "account_number", "account_name", "acc_no"] -> "account"
      header_lower in ["category", "transaction_category", "cat"] -> "category"
      header_lower in ["currency", "curr", "ccy", "currency_code"] -> "currency"
      true -> nil
    end
  end

  # defp detect_field_type(_), do: nil # This clause is unreachable as headers are expected to be binaries

  # Process data rows and create transactions
  defp process_data_rows(data_rows, column_mapping, uploaded_file) do
    data_rows
    |> Enum.with_index(1)
    |> Enum.reduce({[], []}, fn {row, row_number}, {transactions, errors} ->
      case process_single_row(row, row_number, column_mapping, uploaded_file) do
        {:ok, transaction_attrs} ->
          case Reconciliation.create_transaction(transaction_attrs) do
            {:ok, transaction} -> {[transaction | transactions], errors}
            {:error, changeset} ->
              error_msg = "Row #{row_number}: #{format_changeset_errors(changeset)}"
              {transactions, [error_msg | errors]}
          end

        {:error, error_msg} ->
          {transactions, ["Row #{row_number}: #{error_msg}" | errors]}
      end
    end)
    |> then(fn {transactions, errors} -> {Enum.reverse(transactions), Enum.reverse(errors)} end)
  end

  # Process data rows with progress tracking
  defp process_data_rows_with_progress(data_rows, column_mapping, uploaded_file, progress_callback) do
    total_rows = length(data_rows)
    start_time = System.monotonic_time(:millisecond)

    progress_callback.(%{
      status: "processing",
      progress: 75,
      message: "Processing #{total_rows} transaction records...",
      total_records: total_rows,
      records_processed: 0
    })

    {transactions, errors} = data_rows
    |> Enum.with_index(1)
    |> Enum.reduce({[], []}, fn {row, row_number}, {transactions, errors} ->
      # Calculate progress
      progress_percent = min(75 + trunc((row_number / total_rows) * 20), 95)

      # Send progress update every 10 rows or for the last row
      if rem(row_number, 10) == 0 or row_number == total_rows do
        progress_callback.(%{
          status: "processing",
          progress: progress_percent,
          message: "Processing transaction #{row_number} of #{total_rows}...",
          total_records: total_rows,
          records_processed: row_number
        })
      end

      case process_single_row(row, row_number, column_mapping, uploaded_file) do
        {:ok, transaction_attrs} ->
          case Reconciliation.create_transaction(transaction_attrs) do
            {:ok, transaction} -> {[transaction | transactions], errors}
            {:error, changeset} ->
              error_msg = "Row #{row_number}: #{format_changeset_errors(changeset)}"
              {transactions, [error_msg | errors]}
          end

        {:error, error_msg} ->
          {transactions, ["Row #{row_number}: #{error_msg}" | errors]}
      end
    end)

    end_time = System.monotonic_time(:millisecond)
    processing_time = end_time - start_time

    # Final progress update
    progress_callback.(%{
      status: "complete",
      progress: 100,
      message: "Processing complete!",
      total_records: total_rows,
      records_processed: total_rows,
      processing_time: processing_time
    })

    {Enum.reverse(transactions), Enum.reverse(errors)}
  end

  # Process a single row and extract transaction data
  defp process_single_row(row, row_number, column_mapping, uploaded_file) do
    try do
      # Extract basic fields
      date = extract_date(row, column_mapping)
      amount = extract_amount(row, column_mapping)

      # Validate required fields
      if is_nil(amount) do
        {:error, "Missing or invalid amount"}
      else
        transaction_attrs = %{
          uploaded_file_id: uploaded_file.id,
          reconciliation_run_id: uploaded_file.reconciliation_run_id,
          row_number: row_number,
          transaction_date: date,
          transaction_id: extract_field(row, column_mapping, "transaction_id"),
          reference: extract_field(row, column_mapping, "reference"),
          description: extract_field(row, column_mapping, "description"),
          amount: amount,
          transaction_type: extract_field(row, column_mapping, "type"),
          account: extract_field(row, column_mapping, "account"),
          category: extract_field(row, column_mapping, "category"),
          currency: extract_field(row, column_mapping, "currency"),
          raw_data: %{
            "original_row" => row,
            "row_number" => row_number
          }
        }

        {:ok, transaction_attrs}
      end
    rescue
      error ->
        {:error, "Processing error: #{Exception.message(error)}"}
    end
  end

  # Extract date from row
  defp extract_date(row, column_mapping) do
    case extract_field(row, column_mapping, "date") do
      nil -> nil
      date_str when is_binary(date_str) -> parse_date(date_str)
      date_val -> parse_date(to_string(date_val))
    end
  end

  # Extract amount from row (handle debit/credit columns or amount + transaction_type)
  defp extract_amount(row, column_mapping) do
    cond do
      # Case 1: Single amount column with transaction_type column
      Map.has_key?(column_mapping, "amount") and Map.has_key?(column_mapping, "type") ->
        amount = extract_field(row, column_mapping, "amount") |> parse_amount()
        transaction_type = extract_field(row, column_mapping, "type")

        if amount do
          case transaction_type do
            "debit" -> Decimal.negate(amount)  # Debit is negative
            _ -> amount  # Credit, transfer, fee, interest, other are positive
          end
        else
          nil
        end

      # Case 2: Single amount column without transaction_type (assume positive)
      Map.has_key?(column_mapping, "amount") ->
        extract_field(row, column_mapping, "amount") |> parse_amount()

      # Case 3: Separate debit and credit columns
      Map.has_key?(column_mapping, "debit") and Map.has_key?(column_mapping, "credit") ->
        debit = extract_field(row, column_mapping, "debit") |> parse_amount()
        credit = extract_field(row, column_mapping, "credit") |> parse_amount()

        cond do
          debit && credit -> nil  # Invalid: both debit and credit
          debit -> Decimal.negate(debit)  # Debit is negative
          credit -> credit  # Credit is positive
          true -> nil
        end

      true -> nil
    end
  end

  # Extract field value from row
  defp extract_field(row, column_mapping, field) do
    case Map.get(column_mapping, field) do
      nil -> nil
      index when is_integer(index) ->
        case Enum.at(row, index) do
          nil -> nil
          "" -> nil
          value -> String.trim(to_string(value))
        end
    end
  end

  # Parse date string
  defp parse_date(date_str) when is_binary(date_str) do
    date_str = String.trim(date_str)

    # Try different date formats
    formats = [
      "{M}/{D}/{YYYY}",
      "{D}/{M}/{YYYY}",
      "{YYYY}-{M}-{D}",
      "{D}-{M}-{YYYY}",
      "{M}-{D}-{YYYY}"
    ]

    Enum.find_value(formats, fn format ->
      case Timex.parse(date_str, format) do
        {:ok, datetime} -> Timex.to_date(datetime)
        _ -> nil
      end
    end)
  end

  # defp parse_date(_), do: nil # This clause is unreachable if date_str is always binary or nil from extract_field

  # Parse amount string
  defp parse_amount(nil), do: nil
  defp parse_amount(""), do: nil
  defp parse_amount(amount_str) when is_binary(amount_str) do
    # Clean the amount string
    cleaned = amount_str
    |> String.trim()
    |> String.replace(~r/[$,\s]/, "")  # Remove currency symbols and commas
    |> String.replace("(", "-")       # Convert (123) to -123
    |> String.replace(")", "")

    case Decimal.parse(cleaned) do
      {decimal, ""} -> decimal
      _ -> nil
    end
  end

  # defp parse_amount(amount) when is_number(amount) do # Unreachable: parse_amount is called with string or nil from extract_amount
  #   Decimal.from_float(amount)
  # end

  # defp parse_amount(_), do: nil # Unreachable: previous clauses cover nil and all binaries from extract_field

  # Format changeset errors for display
  defp format_changeset_errors(changeset) do
    changeset.errors
    |> Enum.map(fn {field, {message, _}} -> "#{field} #{message}" end)
    |> Enum.join(", ")
  end

  # Separate transaction rows from summary rows
  defp separate_transaction_and_summary_rows(data_rows) do
    Enum.split_with(data_rows, fn row ->
      # Check if this is a summary row by looking at the first column
      case Enum.at(row, 0) do
        nil -> true  # Empty row, treat as transaction
        "" -> true   # Empty first column, treat as transaction
        first_col when is_binary(first_col) ->
          first_col_lower = String.downcase(String.trim(first_col))
          # Check if this is a summary row
          summary_keywords = ["total_", "net_balance"]
          is_summary = Enum.any?(summary_keywords, &String.starts_with?(first_col_lower, &1))
          not is_summary  # Return true for transaction rows, false for summary rows
        _ -> true  # Non-string first column, treat as transaction
      end
    end)
  end

  # Extract summary data from summary rows
  defp extract_summary_data(summary_rows) do
    summary_data = %{
      file_total_transactions: nil,
      file_total_credits: nil,
      file_total_debits: nil,
      file_total_credit_amount: nil,
      file_total_debit_amount: nil,
      file_net_balance: nil,
      file_total_amount: nil
    }

    Enum.reduce(summary_rows, summary_data, fn row, acc ->
      case Enum.at(row, 0) do
        nil -> acc
        "" -> acc
        first_col when is_binary(first_col) ->
          first_col_lower = String.downcase(String.trim(first_col))

          cond do
            String.starts_with?(first_col_lower, "total_transactions") ->
              count = extract_numeric_value(row, 1)
              Map.put(acc, :file_total_transactions, count)

            String.starts_with?(first_col_lower, "total_credits") ->
              count = extract_numeric_value(row, 1)
              amount = extract_decimal_value(row, 3)
              acc
              |> Map.put(:file_total_credits, count)
              |> Map.put(:file_total_credit_amount, amount)

            String.starts_with?(first_col_lower, "total_debits") ->
              count = extract_numeric_value(row, 1)
              amount = extract_decimal_value(row, 3)
              acc
              |> Map.put(:file_total_debits, count)
              |> Map.put(:file_total_debit_amount, amount)

            String.starts_with?(first_col_lower, "net_balance") ->
              amount = extract_decimal_value(row, 3)
              Map.put(acc, :file_net_balance, amount)

            String.starts_with?(first_col_lower, "total_amount") ->
              amount = extract_decimal_value(row, 3)
              Map.put(acc, :file_total_amount, amount)

            true -> acc
          end
        _ -> acc
      end
    end)
  end

  # Extract numeric value from row at given index
  defp extract_numeric_value(row, index) do
    case Enum.at(row, index) do
      nil -> nil
      "" -> nil
      value when is_binary(value) ->
        case Integer.parse(String.trim(value)) do
          {int, ""} -> int
          _ -> nil
        end
      value when is_integer(value) -> value
      _ -> nil
    end
  end

  # Extract decimal value from row at given index
  defp extract_decimal_value(row, index) do
    case Enum.at(row, index) do
      nil -> nil
      "" -> nil
      value when is_binary(value) -> parse_amount(value)
      value when is_number(value) -> Decimal.from_float(value)
      _ -> nil
    end
  end

  # Calculate summary statistics from processed transactions
  defp calculate_summary_statistics(transactions) do
    total_count = length(transactions)

    {credit_count, credit_amount, debit_count, debit_amount} =
      Enum.reduce(transactions, {0, Decimal.new("0"), 0, Decimal.new("0")}, fn transaction, {cc, ca, dc, da} ->
        amount = transaction.amount || Decimal.new("0")

        if Decimal.positive?(amount) do
          {cc + 1, Decimal.add(ca, amount), dc, da}
        else
          {cc, ca, dc + 1, Decimal.add(da, Decimal.abs(amount))}
        end
      end)

    net_balance = Decimal.sub(credit_amount, debit_amount)
    total_amount = Decimal.add(credit_amount, debit_amount)

    %{
      calculated_total_transactions: total_count,
      calculated_total_credits: credit_count,
      calculated_total_debits: debit_count,
      calculated_total_credit_amount: credit_amount,
      calculated_total_debit_amount: debit_amount,
      calculated_net_balance: net_balance,
      calculated_total_amount: total_amount
    }
  end

  # Validate summary data against calculated values
  defp validate_summary_data(file_summary, calculated_summary) do
    errors = []

    errors = if file_summary.file_total_transactions &&
                file_summary.file_total_transactions != calculated_summary.calculated_total_transactions do
      ["Transaction count mismatch: file=#{file_summary.file_total_transactions}, calculated=#{calculated_summary.calculated_total_transactions}" | errors]
    else
      errors
    end

    errors = if file_summary.file_total_amount && calculated_summary.calculated_total_amount &&
                not amounts_match?(file_summary.file_total_amount, calculated_summary.calculated_total_amount) do
      ["Total amount mismatch: file=#{file_summary.file_total_amount}, calculated=#{calculated_summary.calculated_total_amount}" | errors]
    else
      errors
    end

    %{
      summary_validation_passed: Enum.empty?(errors),
      summary_validation_errors: errors
    }
  end

  # Check if two decimal amounts match within tolerance
  defp amounts_match?(amount1, amount2, tolerance \\ Decimal.new("0.01")) do
    diff = Decimal.sub(amount1, amount2) |> Decimal.abs()
    Decimal.compare(diff, tolerance) != :gt
  end
end
