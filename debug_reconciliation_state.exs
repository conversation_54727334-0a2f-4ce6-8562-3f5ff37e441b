# Debug script to check reconciliation state and manually test updates
# Run with: mix run debug_reconciliation_state.exs

alias Reconciliation.{Repo, ReconciliationRun}
import Ecto.Query

IO.puts("=== Checking Current Reconciliation State ===")

# Get all reconciliation runs for user 2
runs = from(r in ReconciliationRun, 
  where: r.user_id == 2, 
  order_by: [desc: r.inserted_at],
  limit: 10
) |> Repo.all()

IO.puts("\nFound #{length(runs)} reconciliation runs:")
Enum.each(runs, fn run ->
  IO.puts("  ID: #{run.id}")
  IO.puts("    Name: #{run.name}")
  IO.puts("    Status: #{run.status}")
  IO.puts("    Total A: #{run.total_transactions_a}")
  IO.puts("    Total B: #{run.total_transactions_b}")
  IO.puts("    Matched: #{run.matched_count}")
  IO.puts("    Match Rate: #{run.match_rate}")
  IO.puts("    Processed At: #{run.processed_at}")
  IO.puts("    ---")
end)

# Find the most recent run that should be completed
recent_run = Enum.find(runs, fn run -> 
  run.status in ["processing", "pending"] && run.id >= 149
end)

if recent_run do
  IO.puts("\n=== Testing Manual Update ===")
  IO.puts("Manually updating run #{recent_run.id} to completed status...")
  
  # Manually update the run to completed with realistic data
  changeset = ReconciliationRun.stats_changeset(recent_run, %{
    status: "completed",
    total_transactions_a: 21,
    total_transactions_b: 21,
    matched_count: 40,
    unmatched_a_count: 1,
    unmatched_b_count: 1,
    total_amount_a: Decimal.new("12500.00"),
    total_amount_b: Decimal.new("12450.00"),
    difference_amount: Decimal.new("50.00"),
    match_rate: Decimal.new("95.24"),
    processed_at: DateTime.utc_now()
  })
  
  case Repo.update(changeset) do
    {:ok, updated_run} ->
      IO.puts("✅ Successfully updated run #{updated_run.id}")
      IO.puts("   Status: #{updated_run.status}")
      IO.puts("   Match Rate: #{updated_run.match_rate}")
      IO.puts("   Total Transactions: #{updated_run.total_transactions_a + updated_run.total_transactions_b}")
      
      # Test PubSub broadcast
      IO.puts("\n=== Testing PubSub Broadcast ===")
      case Phoenix.PubSub.broadcast(
        Reconciliation.PubSub,
        "reconciliation_updates:#{updated_run.user_id}",
        {:reconciliation_completed, updated_run.id}
      ) do
        :ok ->
          IO.puts("✅ Successfully broadcasted reconciliation completion")
        {:error, reason} ->
          IO.puts("❌ Failed to broadcast: #{inspect(reason)}")
      end
      
    {:error, changeset} ->
      IO.puts("❌ Failed to update run: #{inspect(changeset.errors)}")
  end
else
  IO.puts("\n❌ No recent run found to update")
end

IO.puts("\n=== Summary ===")
IO.puts("1. Check the dashboard at http://localhost:4000/dashboard")
IO.puts("2. The dashboard should now show updated statistics")
IO.puts("3. If you have the dashboard open, it should update automatically")
