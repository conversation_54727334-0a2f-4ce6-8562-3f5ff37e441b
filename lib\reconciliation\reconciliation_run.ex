defmodule Reconciliation.ReconciliationRun do
  use Ecto.Schema
  import Ecto.Changeset

  alias Reconciliation.Accounts.User
  alias Reconciliation.{UploadedFile, Transaction, TransactionMatch}

  schema "reconciliation_runs" do
    field :name, :string
    field :description, :string
    field :status, :string, default: "pending"
    field :total_transactions_a, :integer, default: 0
    field :total_transactions_b, :integer, default: 0
    field :matched_count, :integer, default: 0
    field :unmatched_a_count, :integer, default: 0
    field :unmatched_b_count, :integer, default: 0
    field :total_amount_a, :decimal, default: Decimal.new("0")
    field :total_amount_b, :decimal, default: Decimal.new("0")
    field :difference_amount, :decimal, default: Decimal.new("0")
    field :match_rate, :decimal, default: Decimal.new("0")
    field :processed_at, :utc_datetime
    field :error_message, :string

    belongs_to :user, User
    has_many :uploaded_files, UploadedFile, on_delete: :delete_all
    has_many :transactions, Transaction, on_delete: :delete_all
    has_many :transaction_matches, TransactionMatch, on_delete: :delete_all

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(reconciliation_run, attrs) do
    reconciliation_run
    |> cast(attrs, [
      :name, :description, :status, :total_transactions_a, :total_transactions_b,
      :matched_count, :unmatched_a_count, :unmatched_b_count, :total_amount_a,
      :total_amount_b, :difference_amount, :match_rate, :processed_at, :error_message,
      :user_id
    ])
    |> validate_required([:name, :status, :user_id])
    |> validate_inclusion(:status, ["pending", "processing", "completed", "failed"])
    |> validate_length(:name, min: 1, max: 255)
    |> validate_number(:match_rate, greater_than_or_equal_to: 0, less_than_or_equal_to: 100)
    |> foreign_key_constraint(:user_id)
  end

  @doc """
  Creates a changeset for updating reconciliation run statistics
  """
  def stats_changeset(reconciliation_run, attrs) do
    reconciliation_run
    |> cast(attrs, [
      :total_transactions_a, :total_transactions_b, :matched_count,
      :unmatched_a_count, :unmatched_b_count, :total_amount_a,
      :total_amount_b, :difference_amount, :match_rate, :status, :processed_at
    ])
    |> validate_required([:status])
    |> validate_inclusion(:status, ["pending", "processing", "completed", "failed"])
    |> validate_number(:match_rate, greater_than_or_equal_to: 0, less_than_or_equal_to: 100)
  end

  @doc """
  Creates a changeset for marking a reconciliation run as failed
  """
  def error_changeset(reconciliation_run, error_message) do
    reconciliation_run
    |> cast(%{status: "failed", error_message: error_message}, [:status, :error_message])
    |> validate_required([:status, :error_message])
  end

  @doc """
  Calculates match rate percentage
  """
  def calculate_match_rate(total_transactions, matched_count) when total_transactions > 0 do
    (matched_count / total_transactions * 100)
    |> Decimal.from_float()
    |> Decimal.round(2)
  end

  def calculate_match_rate(_, _), do: Decimal.new("0")

  @doc """
  Returns the file A from uploaded files
  """
  def file_a(%__MODULE__{uploaded_files: files}) when is_list(files) do
    Enum.find(files, &(&1.file_type == "file_a"))
  end

  def file_a(_), do: nil

  @doc """
  Returns the file B from uploaded files
  """
  def file_b(%__MODULE__{uploaded_files: files}) when is_list(files) do
    Enum.find(files, &(&1.file_type == "file_b"))
  end

  def file_b(_), do: nil

  @doc """
  Checks if the reconciliation run is complete
  """
  def completed?(%__MODULE__{status: "completed"}), do: true
  def completed?(_), do: false

  @doc """
  Checks if the reconciliation run is in progress
  """
  def processing?(%__MODULE__{status: status}) when status in ["pending", "processing"], do: true
  def processing?(_), do: false

  @doc """
  Checks if the reconciliation run has failed
  """
  def failed?(%__MODULE__{status: "failed"}), do: true
  def failed?(_), do: false

  @doc """
  Returns the display name for file A
  """
  def file_a_display_name(%__MODULE__{} = run) do
    case file_a(run) do
      %UploadedFile{} = file -> UploadedFile.display_name(file)
      _ -> "First File"
    end
  end

  @doc """
  Returns the display name for file B
  """
  def file_b_display_name(%__MODULE__{} = run) do
    case file_b(run) do
      %UploadedFile{} = file -> UploadedFile.display_name(file)
      _ -> "Second File"
    end
  end

  @doc """
  Returns the short display name for file A
  """
  def file_a_short_name(%__MODULE__{} = run) do
    case file_a(run) do
      %UploadedFile{} = file -> UploadedFile.short_display_name(file, 20)
      _ -> "File A"
    end
  end

  @doc """
  Returns the short display name for file B
  """
  def file_b_short_name(%__MODULE__{} = run) do
    case file_b(run) do
      %UploadedFile{} = file -> UploadedFile.short_display_name(file, 20)
      _ -> "File B"
    end
  end
end
