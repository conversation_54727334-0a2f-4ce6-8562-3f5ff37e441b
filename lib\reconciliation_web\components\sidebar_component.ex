defmodule ReconciliationWeb.SidebarComponent do
  use ReconciliationWeb, :live_component

  def mount(socket) do
    {:ok, socket}
  end

  def update(assigns, socket) do
    {:ok, assign(socket, assigns)}
  end

  # Helper function to determine if a navigation item is active
  def nav_class(current_path, target_path, color \\ "emerald") do
    base_classes = "group flex items-center px-3 py-2.5 text-sm font-medium rounded-xl transition-all duration-200"

    if String.starts_with?(current_path, target_path) do
      case color do
        "slate" -> "#{base_classes} bg-slate-100 text-slate-700 shadow-sm border border-slate-200"
        _ -> "#{base_classes} bg-emerald-100 text-emerald-700 shadow-sm border border-emerald-200"
      end
    else
      case color do
        "slate" -> "#{base_classes} hover:bg-slate-50 hover:text-slate-700 text-gray-700 hover:shadow-sm"
        _ -> "#{base_classes} hover:bg-emerald-50 hover:text-emerald-700 text-gray-700 hover:shadow-sm"
      end
    end
  end

  def icon_class(current_path, target_path, color \\ "emerald") do
    base_classes = "w-5 h-5 mr-3 transition-colors"

    if String.starts_with?(current_path, target_path) do
      case color do
        "slate" -> "#{base_classes} text-slate-600"
        _ -> "#{base_classes} text-emerald-600"
      end
    else
      case color do
        "slate" -> "#{base_classes} text-gray-400 group-hover:text-slate-600"
        _ -> "#{base_classes} text-gray-400 group-hover:text-emerald-600"
      end
    end
  end

  def active_indicator_class(current_path, target_path, color \\ "emerald") do
    base_classes = "ml-auto w-2 h-2 rounded-full transition-opacity"

    if String.starts_with?(current_path, target_path) do
      case color do
        "slate" -> "#{base_classes} bg-slate-500 opacity-100"
        _ -> "#{base_classes} bg-emerald-500 opacity-100"
      end
    else
      case color do
        "slate" -> "#{base_classes} bg-slate-500 opacity-0 group-hover:opacity-100"
        _ -> "#{base_classes} bg-emerald-500 opacity-0 group-hover:opacity-100"
      end
    end
  end
end
