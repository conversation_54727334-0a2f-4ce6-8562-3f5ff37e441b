defmodule ReconciliationWeb.DashboardLive do
  use ReconciliationWeb, :live_view

  def mount(_params, _session, socket) do
    user = socket.assigns.current_user

    # Subscribe to reconciliation updates for real-time dashboard updates
    require Logger
    Logger.info("Dashboard subscribing to reconciliation updates for user #{user.id}")
    Phoenix.PubSub.subscribe(Reconciliation.PubSub, "reconciliation_updates:#{user.id}")

    runs = Reconciliation.list_reconciliation_runs(user.id)
    recent_runs = Enum.take(runs, 5)

    # Calculate dynamic summary values from completed runs only
    completed_runs = Enum.filter(runs, &(&1.status == "completed"))

    # Calculate separate totals for each data source
    total_processed_a = completed_runs |> Enum.map(&(&1.total_transactions_a || 0)) |> Enum.sum()
    total_processed_b = completed_runs |> Enum.map(&(&1.total_transactions_b || 0)) |> Enum.sum()

    # Get sample file names for display
    all_files = runs |> Enum.flat_map(& &1.uploaded_files)
    sample_file_a_name = all_files |> Enum.find(&(&1.file_type == "file_a")) |> case do
      nil -> nil
      file -> Reconciliation.UploadedFile.short_display_name(file, 20)
    end
    sample_file_b_name = all_files |> Enum.find(&(&1.file_type == "file_b")) |> case do
      nil -> nil
      file -> Reconciliation.UploadedFile.short_display_name(file, 20)
    end

    # For reconciliation, total transactions should be the sum of the maximum transactions
    # from each run (since matches pair one transaction from A with one from B)
    total_transactions =
      completed_runs
      |> Enum.map(&(max(&1.total_transactions_a, &1.total_transactions_b)))
      |> Enum.sum()

    total_matched = completed_runs |> Enum.map(&(&1.matched_count || 0)) |> Enum.sum()
    total_discrepancies = total_transactions - total_matched

    # Calculate overall match rate across all completed runs
    match_rate =
      if total_transactions > 0 do
        Float.round(total_matched / total_transactions * 100, 2)
      else
        0.0
      end

    # Calculate additional analytics from real data
    total_files = runs |> Enum.flat_map(& &1.uploaded_files) |> length()
    total_amount_processed =
      completed_runs
      |> Enum.map(&Decimal.add(&1.total_amount_a || Decimal.new("0"), &1.total_amount_b || Decimal.new("0")))
      |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

    pending_runs_count = Enum.count(runs, &(&1.status == "pending"))

    {:ok,
      socket
      |> assign(recent_runs: recent_runs)
      |> assign(total_processed_a: total_processed_a)
      |> assign(total_processed_b: total_processed_b)
      |> assign(total_transactions: total_transactions)
      |> assign(total_matched: total_matched)
      |> assign(match_rate: match_rate)
      |> assign(total_discrepancies: total_discrepancies)
      |> assign(total_files: total_files)
      |> assign(total_amount_processed: total_amount_processed)
      |> assign(pending_runs_count: pending_runs_count)
      |> assign(sample_file_a_name: sample_file_a_name)
      |> assign(sample_file_b_name: sample_file_b_name)
      |> assign(current_path: "/dashboard")
    }
  end

  def render(assigns) do
    ~H"""
    <div class="container mx-auto px-4 py-8">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Financial Overview Dashboard</h1>
        <.link navigate={~p"/reconciliation"} class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium flex items-center">
          <.icon name="hero-plus" class="w-5 h-5 mr-2" />
          New Reconciliation
        </.link>
      </div>

      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <!-- Total Processed A -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
              <span class="text-blue-600 font-semibold text-sm">A</span>
            </div>
            <div>
              <h2 class="text-sm font-semibold text-gray-600 mb-1">
                <%= if @sample_file_a_name do %>
                  <%= @sample_file_a_name %>
                <% else %>
                  First Files (A)
                <% end %>
              </h2>
              <p class="text-2xl font-bold text-blue-600"><%= format_number(@total_processed_a) %></p>
            </div>
          </div>
        </div>

        <!-- Total Processed B -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
              <span class="text-green-600 font-semibold text-sm">B</span>
            </div>
            <div>
              <h2 class="text-sm font-semibold text-gray-600 mb-1">
                <%= if @sample_file_b_name do %>
                  <%= @sample_file_b_name %>
                <% else %>
                  Second Files (B)
                <% end %>
              </h2>
              <p class="text-2xl font-bold text-green-600"><%= format_number(@total_processed_b) %></p>
            </div>
          </div>
        </div>

        <!-- Combined Total -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-sm font-semibold text-gray-600 mb-2">Total Transactions</h2>
          <p class="text-2xl font-bold text-gray-900"><%= format_number(@total_transactions) %></p>
        </div>

        <!-- Matching Rate -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-sm font-semibold text-gray-600 mb-2">Matching Rate</h2>
          <p class="text-2xl font-bold text-green-600"><%= @match_rate %>%</p>
        </div>

        <!-- Discrepancies -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-sm font-semibold text-gray-600 mb-2">Discrepancies Found</h2>
          <p class="text-2xl font-bold text-red-600"><%= format_number(@total_discrepancies) %></p>
        </div>
      </div>

      <!-- Recent Activity / Overview -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold text-gray-800">Recent Reconciliation Runs</h2>
          <.link navigate={~p"/reconciliation"} class="text-indigo-600 hover:text-indigo-800 font-medium">
            View All →
          </.link>
        </div>

        <%= if @recent_runs && Enum.any?(@recent_runs) do %>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Match Rate</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <%= for run <- @recent_runs do %>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      <%= run.name %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= if run.processed_at do %>
                        <%= Calendar.strftime(run.processed_at, "%b %d, %Y") %>
                      <% else %>
                        <%= Calendar.strftime(run.inserted_at, "%b %d, %Y") %>
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class={[
                        "inline-flex px-2 py-1 text-xs font-semibold rounded-full",
                        case run.status do
                          "completed" -> "bg-green-100 text-green-800"
                          "processing" -> "bg-yellow-100 text-yellow-800"
                          "failed" -> "bg-red-100 text-red-800"
                          _ -> "bg-gray-100 text-gray-800"
                        end
                      ]}>
                        <%= String.capitalize(run.status) %>
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= if run.match_rate && Decimal.compare(run.match_rate, Decimal.new("0")) == :gt do %>
                        <%= Decimal.to_string(run.match_rate, :normal) %>%
                      <% else %>
                        0.00%
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <%= if run.status == "completed" do %>
                        <.link navigate={~p"/reconciliation/#{run.id}/results"} class="text-indigo-600 hover:text-indigo-900">
                          View Results
                        </.link>
                      <% else %>
                        <span class="text-gray-400">Processing...</span>
                      <% end %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        <% else %>
          <div class="text-center py-8">
            <.icon name="hero-document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500 mb-4">No reconciliation runs yet</p>
            <.link navigate={~p"/reconciliation"} class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium">
              Start Your First Reconciliation
            </.link>
          </div>
        <% end %>
      </div>

      <!-- Additional Analytics -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
              <.icon name="hero-clock" class="w-6 h-6" />
            </div>
            <div class="ml-4">
              <h3 class="text-sm font-medium text-gray-500">Avg Processing Time</h3>
              <p class="text-2xl font-semibold text-gray-900">-</p>
            </div>
          </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600">
              <.icon name="hero-currency-dollar" class="w-6 h-6" />
            </div>
            <div class="ml-4">
              <h3 class="text-sm font-medium text-gray-500">Amount Reconciled</h3>
              <p class="text-2xl font-semibold text-gray-900"><%= format_currency(@total_amount_processed) %></p>
            </div>
          </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
              <.icon name="hero-document-check" class="w-6 h-6" />
            </div>
            <div class="ml-4">
              <h3 class="text-sm font-medium text-gray-500">Files Processed</h3>
              <p class="text-2xl font-semibold text-gray-900"><%= format_number(@total_files) %></p>
            </div>
          </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-orange-100 text-orange-600">
              <.icon name="hero-exclamation-triangle" class="w-6 h-6" />
            </div>
            <div class="ml-4">
              <h3 class="text-sm font-medium text-gray-500">Pending Review</h3>
              <p class="text-2xl font-semibold text-gray-900"><%= format_number(@pending_runs_count) %></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Matching Trends Chart -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Matching Trends</h2>
          <div class="h-64">
            <canvas id="matchingTrendsChart" class="w-full h-full"></canvas>
          </div>
          <div class="mt-4 grid grid-cols-3 gap-4 text-center">
            <div>
              <div class="text-2xl font-bold text-emerald-600"><%= @match_rate %>%</div>
              <div class="text-sm text-gray-500">Avg Match Rate</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-blue-600"><%= format_number(@total_matched) %></div>
              <div class="text-sm text-gray-500">Total Matched</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-orange-600"><%= format_number(@total_discrepancies) %></div>
              <div class="text-sm text-gray-500">Discrepancies</div>
            </div>
          </div>
        </div>

        <!-- Discrepancy Categories Chart -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Discrepancy Categories</h2>
          <div class="h-64">
            <canvas id="discrepancyChart" class="w-full h-full"></canvas>
          </div>
          <div class="mt-4 space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Amount Mismatch</span>
              </div>
              <span class="text-sm font-semibold text-gray-800">24 (38.7%)</span>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Date Variance</span>
              </div>
              <span class="text-sm font-semibold text-gray-800">18 (29.0%)</span>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Missing Reference</span>
              </div>
              <span class="text-sm font-semibold text-gray-800">12 (19.4%)</span>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Duplicate Entry</span>
              </div>
              <span class="text-sm font-semibold text-gray-800">8 (12.9%)</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Chart.js Script -->
      <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
      <script>
        document.addEventListener('DOMContentLoaded', function() {
          // Matching Trends Line Chart
          const matchingCtx = document.getElementById('matchingTrendsChart').getContext('2d');
          new Chart(matchingCtx, {
            type: 'line',
            data: {
              labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
              datasets: [{
                label: 'Match Rate %',
                data: [92.5, 94.2, 93.8, 95.1, 96.3, 95.3],
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  display: false
                }
              },
              scales: {
                y: {
                  beginAtZero: false,
                  min: 90,
                  max: 100,
                  grid: {
                    color: '#f3f4f6'
                  }
                },
                x: {
                  grid: {
                    color: '#f3f4f6'
                  }
                }
              }
            }
          });

          // Discrepancy Categories Doughnut Chart
          const discrepancyCtx = document.getElementById('discrepancyChart').getContext('2d');
          new Chart(discrepancyCtx, {
            type: 'doughnut',
            data: {
              labels: ['Amount Mismatch', 'Date Variance', 'Missing Reference', 'Duplicate Entry'],
              datasets: [{
                data: [24, 18, 12, 8],
                backgroundColor: [
                  '#ef4444',
                  '#f97316',
                  '#eab308',
                  '#a855f7'
                ],
                borderWidth: 0
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  display: false
                }
              }
            }
          });
        });
      </script>
    </div>
    """
  end

  @impl true
  def handle_info({:reconciliation_completed, reconciliation_run_id}, socket) do
    require Logger
    Logger.info("Dashboard received reconciliation completion notification for run #{reconciliation_run_id}")

    # Refresh the dashboard data when a reconciliation completes
    user = socket.assigns.current_user
    runs = Reconciliation.list_reconciliation_runs(user.id)
    recent_runs = Enum.take(runs, 5)

    # Calculate dynamic summary values from completed runs only
    completed_runs = Enum.filter(runs, &(&1.status == "completed"))

    # Calculate separate totals for each data source
    total_processed_a = completed_runs |> Enum.map(&(&1.total_transactions_a || 0)) |> Enum.sum()
    total_processed_b = completed_runs |> Enum.map(&(&1.total_transactions_b || 0)) |> Enum.sum()

    # For reconciliation, total transactions should be the sum of the maximum transactions
    # from each run (since matches pair one transaction from A with one from B)
    total_transactions =
      completed_runs
      |> Enum.map(&(max(&1.total_transactions_a, &1.total_transactions_b)))
      |> Enum.sum()

    total_matched = completed_runs |> Enum.map(&(&1.matched_count || 0)) |> Enum.sum()
    total_discrepancies = total_transactions - total_matched

    # Calculate overall match rate across all completed runs
    match_rate =
      if total_transactions > 0 do
        Float.round(total_matched / total_transactions * 100, 2)
      else
        0.0
      end

    # Calculate additional analytics from real data
    total_files = runs |> Enum.flat_map(& &1.uploaded_files) |> length()
    total_amount_processed =
      completed_runs
      |> Enum.map(&Decimal.add(&1.total_amount_a || Decimal.new("0"), &1.total_amount_b || Decimal.new("0")))
      |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

    pending_runs_count = Enum.count(runs, &(&1.status == "pending"))

    # Get sample file names for display
    all_files = runs |> Enum.flat_map(& &1.uploaded_files)
    sample_file_a_name = all_files |> Enum.find(&(&1.file_type == "file_a")) |> case do
      nil -> nil
      file -> Reconciliation.UploadedFile.short_display_name(file, 20)
    end
    sample_file_b_name = all_files |> Enum.find(&(&1.file_type == "file_b")) |> case do
      nil -> nil
      file -> Reconciliation.UploadedFile.short_display_name(file, 20)
    end

    socket =
      socket
      |> assign(recent_runs: recent_runs)
      |> assign(total_processed_a: total_processed_a)
      |> assign(total_processed_b: total_processed_b)
      |> assign(total_transactions: total_transactions)
      |> assign(total_matched: total_matched)
      |> assign(match_rate: match_rate)
      |> assign(total_discrepancies: total_discrepancies)
      |> assign(total_files: total_files)
      |> assign(total_amount_processed: total_amount_processed)
      |> assign(pending_runs_count: pending_runs_count)
      |> assign(sample_file_a_name: sample_file_a_name)
      |> assign(sample_file_b_name: sample_file_b_name)
      |> put_flash(:info, "Dashboard updated with latest reconciliation results!")

    {:noreply, socket}
  end

  # Handle other PubSub messages
  def handle_info(_msg, socket) do
    {:noreply, socket}
  end

  # Helper functions
  defp format_number(number) when is_nil(number), do: "0"
  defp format_number(number) do
    # Simple number formatting without external dependencies
    number
    |> to_string()
    |> String.reverse()
    |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
    |> String.reverse()
  end

  defp format_currency(amount) when is_nil(amount), do: "0.00"
  defp format_currency(amount) do
    Decimal.to_string(amount, :normal)
  end

  defp format_currency_with_symbol(amount, currency) when is_nil(amount), do: "#{currency_symbol(currency)}0.00"
  defp format_currency_with_symbol(amount, currency) do
    "#{currency_symbol(currency)}#{Decimal.to_string(amount, :normal)}"
  end

  defp currency_symbol(currency) when is_binary(currency) do
    case String.upcase(currency) do
      "USD" -> "$"
      "EUR" -> "€"
      "GBP" -> "£"
      "JPY" -> "¥"
      "CAD" -> "C$"
      "AUD" -> "A$"
      "MWK" -> "MK "
      "ZAR" -> "R "
      "KES" -> "KSh "
      "TZS" -> "TSh "
      "UGX" -> "USh "
      "ZMW" -> "ZK "
      "BWP" -> "P "
      "NAD" -> "N$ "
      "SZL" -> "L "
      "LSL" -> "M "
      other -> "#{other} "
    end
  end
  defp currency_symbol(_), do: ""
end
