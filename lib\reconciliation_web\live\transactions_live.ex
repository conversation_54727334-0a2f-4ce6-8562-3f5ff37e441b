defmodule ReconciliationWeb.TransactionsLive do
  use ReconciliationWeb, :live_view

  @refresh_interval 5_000 # 5 seconds

  def handle_event("change_page", %{"page" => page}, socket) do
    page_num = String.to_integer(page)
    {:noreply,
     socket
     |> assign(:current_page, page_num)
     |> update_paginated_transactions()
    }
  end

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_user

    # Get all transactions for the user's reconciliation runs
    transactions = get_user_transactions(user.id)
    reconciliation_runs = Reconciliation.list_reconciliation_runs(user.id)

    if connected?(socket), do: Process.send_after(self(), :refresh_transactions, @refresh_interval)

    {:ok,
     socket
     |> assign(:page_title, "Transactions")
     |> assign(:transactions, transactions)
     |> assign(:all_transactions, transactions)
     |> assign(:reconciliation_runs, reconciliation_runs)
     |> assign(:selected_run_id, nil)
     |> assign(:search_query, "")
     |> assign(:current_page, 1)
     |> assign(:per_page, 12)
     |> update_transaction_totals(transactions)
     |> update_paginated_transactions()
    }
  end

  @impl true
  def handle_event("filter_by_run", %{"run_id" => ""}, socket) do
    user = socket.assigns.current_user
    transactions = get_user_transactions(user.id)

    {:noreply,
     socket
     |> assign(:transactions, transactions)
     |> assign(:all_transactions, transactions)
     |> assign(:selected_run_id, nil)
     |> assign(:current_page, 1)
     |> update_transaction_totals(transactions)
     |> update_paginated_transactions()
    }
  end

  def handle_event("filter_by_run", %{"run_id" => run_id}, socket) do
    # Convert string to integer for database query
    run_id_int = String.to_integer(run_id)

    transactions =
      run_id_int
      |> Reconciliation.get_transactions()
      |> sort_transactions_by_reference_and_file()

    {:noreply,
     socket
     |> assign(:transactions, transactions)
     |> assign(:all_transactions, transactions)
     |> assign(:selected_run_id, run_id)  # Keep as string for UI
     |> assign(:current_page, 1)
     |> update_transaction_totals(transactions)
     |> update_paginated_transactions()
    }
  end

  def handle_event("search", %{"search" => query}, socket) do
    user = socket.assigns.current_user
    transactions_to_filter = if socket.assigns.selected_run_id do
      Reconciliation.get_transactions(socket.assigns.selected_run_id)
    else
      get_user_transactions(user.id)
    end

    filtered_transactions =
      transactions_to_filter
      |> filter_transactions(query)
      |> sort_transactions_by_reference_and_file()

    {:noreply,
     socket
     |> assign(:transactions, filtered_transactions)
     |> assign(:all_transactions, filtered_transactions)
     |> assign(:search_query, query)
     |> assign(:current_page, 1)
     |> update_transaction_totals(filtered_transactions)
     |> update_paginated_transactions()
    }
  end

  @impl true
  def handle_event("run_matching_engine", _params, socket) do
    require Logger
    run_id = socket.assigns.selected_run_id
    if run_id do
      try do
        # Convert string to integer if needed
        run_id_int = if is_binary(run_id), do: String.to_integer(run_id), else: run_id

        run = Reconciliation.get_reconciliation_run!(run_id_int)
        Logger.info("Starting matching for run #{run_id_int}")

        # Get current settings and temporarily enable fuzzy matching for better results
        {:ok, settings} = Reconciliation.get_or_create_settings(run.user_id)
        Logger.info("Current settings: exact=#{settings.auto_match_exact}, fuzzy=#{settings.auto_match_fuzzy}")

        # Temporarily enable fuzzy matching if it's disabled
        if not settings.auto_match_fuzzy do
          Logger.info("Temporarily enabling fuzzy matching for better results")
          {:ok, _updated_settings} = Reconciliation.update_settings(settings, %{auto_match_fuzzy: true})
        end

        # Run the matching engine
        {:ok, matches} = Reconciliation.Services.MatchingEngine.match_transactions(run)
        Logger.info("Matching completed. Found #{length(matches)} matches")

        # Refresh transactions with updated match status
        transactions =
          run_id_int
          |> Reconciliation.get_transactions()
          |> sort_transactions_by_reference_and_file()

        # Count matched vs unmatched
        matched_count = Enum.count(transactions, & &1.is_matched)
        unmatched_count = length(transactions) - matched_count

        message = "Matching completed! Found #{length(matches)} matches. #{matched_count} transactions matched, #{unmatched_count} unmatched."

        {:noreply,
          socket
          |> assign(:transactions, transactions)
          |> assign(:all_transactions, transactions)
          |> update_transaction_totals(transactions)
          |> update_paginated_transactions()
          |> put_flash(:info, message)
        }
      rescue
        error ->
          Logger.error("Error running matching: #{inspect(error)}")
          {:noreply, put_flash(socket, :error, "Error running matching: #{Exception.message(error)}")}
      end
    else
      {:noreply, put_flash(socket, :error, "Please select a reconciliation run first.")}
    end
  end

  @impl true
  def handle_info(:refresh_transactions, socket) do
    user = socket.assigns.current_user
    transactions =
      if socket.assigns.selected_run_id do
        socket.assigns.selected_run_id
        |> Reconciliation.get_transactions()
        |> sort_transactions_by_reference_and_file()
      else
        get_user_transactions(user.id)
      end
    Process.send_after(self(), :refresh_transactions, @refresh_interval)
    {:noreply,
     socket
     |> assign(:transactions, transactions)
     |> assign(:all_transactions, transactions)
     |> update_transaction_totals(transactions)
     |> update_paginated_transactions()
    }
  end

  # Helper functions
  defp get_user_transactions(user_id) do
    user_id
    |> Reconciliation.list_reconciliation_runs()
    |> Enum.flat_map(&Reconciliation.get_transactions(&1.id))
    |> sort_transactions_by_reference_and_file()
  end

  defp update_paginated_transactions(socket) do
    all_transactions = socket.assigns.all_transactions
    current_page = socket.assigns.current_page
    per_page = socket.assigns.per_page

    total_pages = ceil(length(all_transactions) / per_page)
    start_index = (current_page - 1) * per_page

    paginated_transactions =
      all_transactions
      |> Enum.drop(start_index)
      |> Enum.take(per_page)

    socket
    |> assign(:paginated_transactions, paginated_transactions)
    |> assign(:total_pages, total_pages)
    |> assign(:total_records, length(all_transactions))
  end

  defp sort_transactions_by_reference_and_file(transactions) do
    Enum.sort_by(transactions, fn transaction ->
      file_type = case transaction.uploaded_file do
        %{file_type: "file_a"} -> "a"
        %{file_type: "file_b"} -> "b"
        _ -> "z"  # Put unknown file types at the end
      end

      reference = transaction.reference || "zzz"  # Put nil references at the end

      [reference, file_type]
    end)
  end

  defp filter_transactions(transactions, ""), do: transactions
  defp filter_transactions(transactions, query) do
    query_lower = String.downcase(query)

    Enum.filter(transactions, fn transaction ->
      String.contains?(String.downcase(transaction.description || ""), query_lower) ||
      String.contains?(String.downcase(transaction.reference || ""), query_lower) ||
      String.contains?(String.downcase(to_string(transaction.amount)), query_lower)
    end)
  end

  defp update_transaction_totals(socket, transactions) do
    {total_debits, total_credits} =
      Enum.reduce(transactions, {Decimal.new(0), Decimal.new(0)}, fn transaction, {debits_acc, credits_acc} ->
        cond do
          Decimal.negative?(transaction.amount) ->
            {Decimal.add(debits_acc, Decimal.abs(transaction.amount)), credits_acc}
          Decimal.positive?(transaction.amount) ->
            {debits_acc, Decimal.add(credits_acc, transaction.amount)}
          true ->
            {debits_acc, credits_acc}
        end
      end)

    socket
    |> assign(:total_debits, total_debits)
    |> assign(:total_credits, total_credits)
  end

  defp format_currency(amount) when is_nil(amount), do: "0.00"
  defp format_currency(amount) do
    Decimal.to_string(amount, :normal)
  end

  defp format_currency_with_symbol(amount, currency) when is_nil(amount), do: "#{currency_symbol(currency)}0.00"
  defp format_currency_with_symbol(amount, currency) do
    "#{currency_symbol(currency)}#{Decimal.to_string(amount, :normal)}"
  end

  defp currency_symbol(currency) when is_binary(currency) do
    case String.upcase(currency) do
      "USD" -> "$"
      "EUR" -> "€"
      "GBP" -> "£"
      "JPY" -> "¥"
      "CAD" -> "C$"
      "AUD" -> "A$"
      "MWK" -> "MK "
      "ZAR" -> "R "
      "KES" -> "KSh "
      "TZS" -> "TSh "
      "UGX" -> "USh "
      "ZMW" -> "ZK "
      "BWP" -> "P "
      "NAD" -> "N$ "
      "SZL" -> "L "
      "LSL" -> "M "
      other -> "#{other} "
    end
  end
  defp currency_symbol(_), do: ""

  defp format_date(nil), do: "-"
  defp format_date(date), do: Calendar.strftime(date, "%b %d, %Y")

  defp match_status_badge(true) do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
      Matched
    </span>
    """
  end

  defp match_status_badge(false) do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
      Unmatched
    </span>
    """
  end

  defp file_type_badge("file_a") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
      File A
    </span>
    """
  end

  defp file_type_badge("file_b") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
      File B
    </span>
    """
  end

  defp file_type_badge(_) do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
      Unknown
    </span>
    """
  end

  defp transaction_type_badge("debit") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
      Debit
    </span>
    """
  end

  defp transaction_type_badge("credit") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
      Credit
    </span>
    """
  end

  defp transaction_type_badge("transfer") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
      Transfer
    </span>
    """
  end

  defp transaction_type_badge("fee") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
      Fee
    </span>
    """
  end

  defp transaction_type_badge("interest") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
      Interest
    </span>
    """
  end

  defp transaction_type_badge("other") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-indigo-100 text-indigo-800">
      Other
    </span>
    """
  end

  defp transaction_type_badge(nil) do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-500">
      N/A
    </span>
    """
  end

  defp transaction_type_badge(type) when is_binary(type) do
    assigns = %{type: String.capitalize(type)}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
      <%= @type %>
    </span>
    """
  end

  defp transaction_type_badge(_) do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-500">
      Unknown
    </span>
    """
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <h2 class="text-xl font-bold mb-4">Transactions</h2>
      <div class="mb-4 flex items-center gap-4">
        <form phx-change="filter_by_run">
          <label for="run_id">Reconciliation Run:</label>
          <select name="run_id" id="run_id" class="border rounded px-2 py-1">
            <option value="">All</option>
            <%= for run <- @reconciliation_runs do %>
              <option value={run.id} selected={@selected_run_id == to_string(run.id)}><%= run.name %></option>
            <% end %>
          </select>
        </form>
        <form phx-submit="search">
          <input name="search" value={@search_query} placeholder="Search..." class="border rounded px-2 py-1" />
          <button type="submit" class="ml-2 px-3 py-1 bg-blue-500 text-white rounded">Search</button>
        </form>
        <%= if @selected_run_id do %>
          <button phx-click="run_matching_engine" class="ml-4 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition">Run Matching Engine</button>
        <% end %>
      </div>
      <table class="min-w-full bg-white border border-gray-200 table-auto">
        <thead>
          <tr>
            <th class="py-2 px-4 border-b text-left text-sm font-semibold text-gray-700 w-24">Date</th>
            <th class="py-2 px-4 border-b text-left text-sm font-semibold text-gray-700">Description</th>
            <th class="py-2 px-4 border-b text-left text-sm font-semibold text-gray-700 w-32">Reference</th>
            <th class="py-2 px-4 border-b text-left text-sm font-semibold text-gray-700 w-24">Amount</th>
            <th class="py-2 px-4 border-b text-left text-sm font-semibold text-gray-700 w-20">Type</th>
            <th class="py-2 px-4 border-b text-left text-sm font-semibold text-gray-700 w-20">Matched</th>
            <th class="py-2 px-4 border-b text-left text-sm font-semibold text-gray-700 w-40">Source File</th>
          </tr>
        </thead>
        <tbody class="text-sm divide-y divide-gray-200">
          <%= for transaction <- @paginated_transactions do %>
            <tr>
              <td class="py-2 px-4 border-b"><%= format_date(transaction.inserted_at) %></td>
              <td class="py-2 px-4 border-b"><%= transaction.description %></td>
              <td class="py-2 px-4 border-b"><%= transaction.reference %></td>
              <td class="py-2 px-4 border-b"><%= format_currency(transaction.amount) %></td>
              <td class="py-2 px-4 border-b"><%= transaction_type_badge(transaction.transaction_type) %></td>
              <td class="py-2 px-4 border-b"><%= match_status_badge(transaction.is_matched) %></td>
              <td class="py-2 px-4 border-b">
                <%= if transaction.uploaded_file do %>
                  <div class="flex items-center">
                    <%= file_type_badge(transaction.uploaded_file.file_type) %>
                    <span class="ml-2 text-sm text-gray-600 whitespace-nowrap" title={Reconciliation.UploadedFile.display_name(transaction.uploaded_file)}>
                      <%= Reconciliation.UploadedFile.display_name(transaction.uploaded_file) %>
                    </span>
                  </div>
                <% else %>
                  <span class="text-gray-400">Unknown</span>
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>

      <!-- Pagination Controls -->
      <div class="mt-4 flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Showing <%= (@current_page - 1) * @per_page + 1 %> to <%= min(@current_page * @per_page, @total_records) %> of <%= @total_records %> transactions
        </div>

        <div class="flex items-center space-x-2">
          <%= if @current_page > 1 do %>
            <button phx-click="change_page" phx-value-page={@current_page - 1} class="px-3 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">
              Previous
            </button>
          <% end %>

          <%= for page <- max(1, @current_page - 2)..min(@total_pages, @current_page + 2) do %>
            <button
              phx-click="change_page"
              phx-value-page={page}
              class={"px-3 py-1 rounded transition " <> if page == @current_page, do: "bg-blue-500 text-white", else: "bg-gray-200 text-gray-700 hover:bg-gray-300"}
            >
              <%= page %>
            </button>
          <% end %>

          <%= if @current_page < @total_pages do %>
            <button phx-click="change_page" phx-value-page={@current_page + 1} class="px-3 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">
              Next
            </button>
          <% end %>
        </div>
      </div>

      <div class="mt-4">
        <span class="font-semibold">Total Debits:</span> <%= format_currency(@total_debits) %>
        <span class="font-semibold ml-4">Total Credits:</span> <%= format_currency(@total_credits) %>
      </div>
    </div>
    """
  end
end
